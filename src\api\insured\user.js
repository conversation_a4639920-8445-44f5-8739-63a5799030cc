import requestMock from "@/utils/requestMock";

// 查询参保人列表
export function listUsers(query) {
  return requestMock({
    // 患者足迹查询接口地址
    url: "/insured/users/list",
    method: "get",
    params: query,
  });
}

// 获取指定用户的用户信息详情
export function getUserDetail(userId) {
  return requestMock({
    url: "/insured/users/detail?userId=" + userId,
    method: "get",
  });
}
// 获取指定参保人的信息详情
export function getInsuredUserDetail(userId) {
  return requestMock({
    url: "/insured/insureduser/detail?userId=" + userId,
    method: "get",
  });
}

// 查询参保人理赔记录
export function getInsuredRecord(userId) {
  return requestMock({
    url: "/insured/users/record?userId=" + userId,
    method: "get",
  });
}
