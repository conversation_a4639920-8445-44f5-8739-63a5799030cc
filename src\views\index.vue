<template>
  <div class="page-container">
    <div class="main-content">
      <!-- 左侧内容区 -->
      <div class="left-section">
        <div class="stat-cards">
          <el-card v-for="(card, index) in statsData" :key="index" shadow="always" class="stat-card">
            <div class="stat-content">
              <div class="stat-title">{{ card.title }} <span style="color: #888">（本月）</span></div>
              <div class="stat-value">
                {{ card.value }}
                <el-icon class="stat-icon" color="#409eff" size="46"><List /></el-icon>
              </div>
              <div class="stat-compare" :class="card.trend">与上月比{{ card.compare > 0 ? '+' : '' }}{{ card.compare }}</div>
            </div>
          </el-card>
        </div>

        <div class="steps-section">
          <el-button type="primary" plain>查看更多>></el-button>
        </div>
      </div>

      <!-- 右侧图表区 -->
      <div class="right-section">
        <div class="chart-box">
          <div class="chart-title">医疗数据统计 <span class="unit">(总量)</span></div>
          <div class="chart-subtitle">月新增：16000<span class="up-arrow">↑</span></div>
          <div class="chart-number">16000</div>
          <!-- <img src="../assets/images/zhu.png" alt="" style="width: 200px" /> -->
          <!-- <h-echarts ref="medicalChart" :option="medicalChartOption" :loading="loading" height="140px"></h-echarts> -->
        </div>
        <div class="chart-box">
          <div class="chart-title">异常数据统计 <span class="unit">(总量)</span></div>
          <div class="chart-subtitle">月新增：0</div>
          <div class="chart-number">8</div>
          <!-- <img src="../assets/images/bing.png" alt="" style="width: 100px; float: right; margin-top: -40px" /> -->
          <!-- <h-echarts ref="errorChart" :option="errorChartOption" :loading="loading" height="140px"></h-echarts> -->
        </div>
      </div>
    </div>

    <!-- 底部数据列表 -->
    <div class="bottom-section">
      <div class="data-list-box">
        <div class="list-header">
          <div class="list-title">异常数据信息通知栏</div>
          <div class="view-history" @click="router.push('/warning/warningList')">查看历史 ></div>
        </div>
        <div class="list-content">
          <table>
            <tr v-for="(item, index) in 7" :key="index">
              <td>诊断信息异常</td>
              <td>2025-09-01 16:03:59</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="chart-box">
        <div class="chart-view">
          <div class="chart-title">数据可视化看板</div>
          <div class="button-view">
            <el-button-group>
              <!-- 点击按钮时更新 dateType 变量 -->
              <!-- 修改：使用 .value 方式更新 ref 变量改为直接赋值 -->
              <el-button :type="dateType === 'year' ? 'primary' : ''" @click="changeButton('year')">年</el-button>
              <el-button :type="dateType === 'month' ? 'primary' : ''" @click="changeButton('month')">月</el-button>
            </el-button-group>
          </div>
        </div>
        <h-echarts ref="medicalChart" :option="medicalChartOption" :loading="loading" height="400px"></h-echarts>
      </div>
    </div>
  </div>
</template>

<script setup>
import HEcharts from '@/components/HEcharts'
import { ref } from 'vue'
import { useRouter } from 'vue-router' // 补充导入 useRouter
// 统计卡片数据
const statsData = ref([
  {
    title: '总医院',
    value: 18,
    compare: 6,
    trend: 'up'
  },
  {
    title: '一中心',
    value: 8,
    compare: -2,
    trend: 'down'
  },
  {
    title: '儿童医院',
    value: 15,
    compare: +5,
    trend: 'up'
  },
  {
    title: '一附属',
    value: 0,
    compare: 0,
    trend: 'normal'
  }
])
const loading = ref(false)
const router = useRouter()
// 定义 dateType 变量，初始值为 'year'
const dateType = ref('month')
const dataList = ref([0, 0, 0, 0, 0, 0, 7000, 4500, 3000, 0, 7000, 4500])
function changeButton(type) {
  // 修改：使用.value 方式更新 ref 变量改为直接赋值
  dateType.value = type
  if(type === 'year') {
    dataList.value = [5500, 0, 3000, 0, 8000, 0, 17000, 42500, 3000, 0, 16000, 4500]
  } else {
    dataList.value = [0, 0, 0, 0, 0, 0, 7000, 4500, 3000, 0, 7000, 4500]}
}

// 医疗数据图表配置
const medicalChartOption = ref({
  grid: {
    top: '10%',
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [
      '2025-01',
      '2025-02',
      '2025-03',
      '2025-04',
      '2025-05',
      '2025-06',
      '2025-07',
      '2025-08',
      '2025-09',
      '2025-10',
      '2025-11',
      '2025-12'
    ],
    axisLine: {
      lineStyle: {
        color: '#E5E6EB'
      }
    },
    axisLabel: {
      color: '#86909C'
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    splitLine: {
      lineStyle: {
        color: '#E5E6EB',
        type: 'dashed'
      }
    },
    axisLabel: {
      color: '#86909C'
    }
  },
  series: [
    {
      data: dataList,
      type: 'bar',
      barWidth: '20px',
      itemStyle: {
        color: '#4080FF'
      }
    }
  ]
})

// 异常数据图表配置
const errorChartOption = ref({
  series: [
    {
      type: 'pie',
      radius: ['65%', '80%'],
      label: {
        show: false
      },
      data: [
        { value: 8, name: '异常数据', itemStyle: { color: '#4080FF' } },
        { value: 92, name: '正常数据', itemStyle: { color: '#E5E6EB' } }
      ]
    }
  ]
})
</script>

<style lang="scss" scoped>
.page-container {
  // padding: 16px;
  // background-color: #f2f3f5;
  min-height: 100vh;
}

.main-content {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.left-section {
  width: 60%;
  background: #fff;
  padding: 20px;
  // border: 1px solid #4080ff;
  border-radius: 2px;

  .title-section {
    margin-bottom: 24px;

    .main-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;

      .status-badge {
        background: #ffc300;
        color: #fff;
        padding: 2px 8px;
        border-radius: 2px;
        font-size: 12px;
        font-weight: normal;
      }
    }

    .address {
      color: #4e5969;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .steps-section {
    // padding: 0 40px;
  }
}
.stat-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    .stat-card {
      :deep(.el-card__body) {
        padding: 20px;
      }

      .stat-content {
        .stat-title {
          color: #666;
          font-size: 14px;
          margin-bottom: 12px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
          display: flex;
          justify-content: space-between;

          .stat-icon {
            background-color: #e8eefe;
            padding: 5px;
            border-radius: 23px;
            margin-top: -10px;
          }
        }

        .stat-compare {
          font-size: 12px;

          &.up {
            color: #f56c6c;
          }
          &.down {
            color: #67c23a;
          }
          &.normal {
            color: #909399;
          }
        }
      }
    }
  }
.right-section {
  flex: 1;
  display: flex;
  gap: 16px;

  .chart-box {
    flex: 1;
    background: #fff;
    padding: 16px;
    border-radius: 2px;

    .chart-title {
      font-size: 14px;
      color: #1d2129;
      margin-bottom: 16px;

      .unit {
        color: #86909c;
        font-size: 12px;
      }
    }

    .chart-subtitle {
      color: #86909c;
      font-size: 13px;
      margin-bottom: 4px;

      .up-arrow {
        color: #f53f3f;
        margin-left: 4px;
      }
    }

    .chart-number {
      font-size: 24px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
    }
  }
}

.bottom-section {
  display: flex;
  .data-list-box {
    width: 30%;
    background: #fff;
    padding: 16px;
    border-radius: 2px;
    margin-right: 20px;
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .list-title {
        font-size: 14px;
        color: #1d2129;
      }

      .view-history {
        color: #4080ff;
        font-size: 13px;
        cursor: pointer;
      }
    }

    .list-content {
      table {
        width: 100%;
        border-collapse: collapse;

        tr {
          border-bottom: 1px solid #e5e6eb;

          td {
            padding: 12px 0;
            color: #4e5969;
            font-size: 13px;

            &:first-child {
              width: 120px;
            }
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
  .chart-box {
    background: #fff;
    flex: 1;
    padding: 16px;
    .chart-title {
      font-size: 14px;
      color: #1d2129;
      margin-bottom: 16px;
      // font-weight: 500;
    }
  }
}

:deep(.el-steps) {
  .el-step__title {
    font-size: 14px;
    color: #1d2129;

    &.is-process {
      color: #4080ff;
      font-weight: normal;
    }

    &.is-wait {
      color: #86909c;
    }
  }

  .el-step__description {
    font-size: 12px;
    color: #86909c;

    &.is-process {
      color: #4080ff;
    }
  }

  .el-step__head {
    .el-step__icon {
      width: 24px;
      height: 24px;

      .el-step__icon-inner {
        font-size: 14px;
      }
    }

    &.is-process {
      color: #4080ff;
      border-color: #4080ff;
    }

    &.is-wait {
      color: #86909c;
      border-color: #86909c;
    }
  }

  .el-step__line {
    background-color: #e5e6eb;
  }
}
.chart-view{
  display: flex;
  justify-content: space-between;
}
</style>
