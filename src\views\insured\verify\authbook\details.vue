<template>
  <div class="insurance-claim-container">
    <div class="container">
      <!-- 申请人信息 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">申请人信息</span>
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">申请人姓名:</span>
                <span class="info-value">{{ info.applyInfo.applyName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">参保人姓名:</span>
                <span class="info-value">{{ info.applyInfo.insuredName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">是申请人的什么:</span>
                <span class="info-value">{{ info.applyInfo.relationship }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">授权截止至:</span>
                <span class="info-value">{{ info.applyInfo.jiezhi }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">查询范围:</span>
                <span class="info-value">{{ info.applyInfo.dateRange }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">签署时间:</span>
                <span class="info-value">{{ info.applyInfo.date }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 理赔材料 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">理赔材料</span>
        </div>
        <div class="info-content">
          <div class="material-images">
            <el-image
              v-for="(item, index) in info.lpCailiao"
              :key="index"
              :src="item"
              fit="cover"
              class="material-image"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="info.lpCailiao"
              :initial-index="index"
            >
            </el-image>
          </div>
        </div>
      </div>
      <!-- 底部按钮 -->
      <div class="action-buttons">
        <el-button type="primary" plain>查看医疗数据查询授权书</el-button>
        <el-button type="primary">授权通过</el-button>
        <el-button type="danger" @click="dialogFormVisible = true">驳回授权</el-button>
      </div>
    </div>
    <div class="flow-div">
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">操作记录</span>
        </div>
        <div class="operation-record">
          <div class="record-item">
            <span class="record-time">2024-09-04 16:15:19</span>
            <div class="record-details">
              <div class="reco-item">
                <span>操作人:</span>
                <span>李四</span>
              </div>
              <div class="reco-item">
                <span>操作行为:</span>
                <span>驳回授权</span>
              </div>
              <div class="reco-item">
                <span>操作备注:</span>
                <span>关系证明中姓名与授权书中姓名不匹配</span>
              </div>
              <div class="reco-item">
                <span>附件图片:</span>
                <div class="material-images">
                  <el-image
                    v-for="(img, index) in info.imgs"
                    :key="index"
                    :src="img"
                    fit="cover"
                    class="material-image"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="info.imgs"
                    :initial-index="index"
                  >
                  </el-image>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogFormVisible" title="驳回备注" width="500" align-center>
    <el-form :model="returnForm" label-width="100px" label-position="right">
      <el-form-item label="驳回原因">
        <el-select v-model="returnForm.region" placeholder="请选择驳回原因">
          <el-option label="理赔文件缺失" value="shanghai" />
        </el-select>
      </el-form-item>
      <el-form-item label="其他原因">
        <el-input v-model="returnForm.name" autocomplete="off" placeholder="请输入其他原因" />
      </el-form-item>
      <el-form-item label="附件图片"> </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRejection"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getAuthorizationInfo } from '@/api/insured/verify'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const info = ref({
  applyInfo: {},
  lpCailiao: [],
  imgs:[]
})
const getInsuredDetails = async () => {
  loading.value = true
  try {
    const res = await getAuthorizationInfo({ id: 1 }) // 假设传入的ID为1
    if (res.code === 200) {
      info.value = res.data
      console.log(info.value)
    } else {
      console.error('获取理赔详情失败:', res.message)
    }
  } catch (error) {
    console.error('请求错误:', error)
  } finally {
    loading.value = false
  }
}
getInsuredDetails()
const dialogFormVisible = ref(false)
const returnForm = ref({
  region: '',
  name: '',
  attachment: ''
})
const handleRejection = () => {
  ElMessageBox.confirm('确定驳回此申请?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '申请已驳回'
    })
    dialogFormVisible.value = false
  })
}
</script>

<style lang="scss" scoped>
.insurance-claim-container {
  display: flex;
  // padding: 20px;
  // background-color: white;
  .container {
    background: #fff;
    flex: 1;
  }
  .flow-div {
    width: 20%;
    background: #fff;
    margin-left: 20px;
  }
  .info-card {
    .card-header {
      padding: 16px;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2d3d;
        position: relative;
        padding-left: 10px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 2px;
          bottom: 2px;
          width: 4px;
          background-color: #409eff;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 0 16px;
      font-size: 14px;

      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        min-height: 28px;

        .info-label {
          font-weight: 500;
          color: #333;
          min-width: 120px;
          margin-right: 10px;
        }

        .info-value {
          color: #666;
          font-weight: 500;
        }
      }

      .sub-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 20px 0 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      }
    }
  }

  .material-images {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .material-image {
      width: 150px;
      height: 100px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;

      :deep(.el-image__inner) {
        object-fit: contain;
      }
    }

    .image-hint {
      color: #909399;
      font-size: 14px;
      margin-bottom: 15px;
      font-style: italic;
    }
  }

  .settlement-item {
    display: flex;
    align-items: center;
    padding: 12px 0;

    .settlement-label {
      font-weight: 500;
      color: #333;
      min-width: 120px;
    }

    .settlement-value {
      color: #666;
      font-weight: 500;
    }

    .el-button {
      margin-left: 10px;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    margin: 30px 20px;
    border-radius: 8px;
  }

  .operation-record {
    padding: 0 30px;
    .record-item {
      margin-bottom: 20px;

      .record-time {
        font-size: 14px;
        margin-bottom: 10px;
        color: #409eff;
        font-weight: 600;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          left: -15px;
          top: 5px;
          bottom: 2px;
          width: 8px;
          background-color: #409eff;
          border-radius: 50%;
          height: 8px;
        }
      }

      .record-details {
        font-size: 14px;
        color: #666;
        .reco-item {
          margin: 20px 0;
          span:first-child {
            display: inline-block;
            width: 80px;
            color: #101010;
            // margin-right: 15px;
          }
        }
      }
    }
  }
}
</style>
