<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" label-width="150px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="申请时间范围" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="医疗机构名称" prop="medicalName">
            <el-select
              v-model="queryParams.medicalName"
              multiple
              filterable
              placeholder="请选择医疗机构名称"
              style="width: 100%"
              clearable
              maxlength="50"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in medicalNameList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人姓名" prop="peopleName">
            <el-input
              v-model="queryParams.peopleName"
              placeholder="请输入联系人姓名"
              maxlength="50"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="联系人手机号码" prop="phoneNumber">
            <el-input
              v-model="queryParams.phoneNumber"
              placeholder="请输入联系人手机号码"
              clearable
              maxlength="50"
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="接入阶段" prop="stage">
            <el-select
              v-model="queryParams.stage"
              placeholder="请选择接入阶段"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in accessStageList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审核处理所属人" prop="assessor">
            <el-select
              v-model="queryParams.assessor"
              placeholder="请选择接入阶段"
              multiple
              filterable
              clearable
              maxlength="50"
              style="width: 100%"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in assessorList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 20px">
        <el-col :span="24" style="text-align: right">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button icon="Plus" @click="handleAdd">手动录入</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="verifyList"
      :row-style="{ height: '45px' }"
    >
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="申请时间" align="center" prop="applyTime" width="200" />
      <el-table-column
        label="医疗机构名称"
        align="center"
        prop="medicalName"
        width="200"
      />
      <el-table-column label="联系人姓名" align="center" prop="peopleName" />
      <el-table-column
        label="联系人手机号码"
        align="center"
        prop="phoneNumber"
      />
      <el-table-column
        label="联系人邮箱"
        align="center"
        prop="email"
        width="200"
      />
      <el-table-column label="接入阶段" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.stage === '1'" type="info">待审核</el-tag>
          <el-tag v-if="scope.row.stage === '2'" type="primary"
            >申请接入</el-tag
          >
          <el-tag v-if="scope.row.stage === '3'" type="warning"
            >数据联调</el-tag
          >
          <el-tag v-if="scope.row.stage === '4'" type="success"
            >接入成功</el-tag
          >
          <el-tag v-if="scope.row.stage === '5'" type="danger">已驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="审核处理所属人"
        align="center"
        prop="assessor"
        width="150"
      />
      <el-table-column
        label="操作"
        width="100"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:post:remove']"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Verify">
import { listVerify } from "@/api/medical/verify";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const verifyList = ref([]);
const loading = ref(true);
const total = ref(0);

// 医疗结构名称
const medicalNameList = ref([
  {
    label: "天津中医院",
    value: "1",
  },
  {
    label: "天津一中心医院",
    value: "2",
  },
]);

// 接入阶段
const accessStageList = ref([
  {
    label: "全部",
    value: "1",
  },
  {
    label: "待审核",
    value: "2",
  },
  {
    label: "申请接入",
    value: "3",
  },
  {
    label: "数据联调",
    value: "4",
  },
  {
    label: "接入成功",
    value: "5",
  },
  {
    label: "已驳回",
    value: "6",
  },
]);

// 审核处理所属人
const assessorList = ref([
  {
    label: "张三",
    value: "1",
  },
  {
    label: "李四",
    value: "2",
  },
]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    medicalName: undefined,
    peopleName: undefined,
    phoneNumber: undefined,
    stage: undefined,
    assessor: undefined,
  },
  dateRange: [],
});

const { queryParams, dateRange, form, rules } = toRefs(data);

/** 查询岗位列表 */
function getList() {
  loading.value = true;
  listVerify(proxy.addDateRange(queryParams.value, dateRange.value)).then(
    (response) => {
      verifyList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  dateRange.value = [];
  handleQuery();
}

function handleAdd() {
  proxy.$router.push("/medical/verifyAdd");
}

function handleDetail(row) {
  proxy.$router.push("/medical/verifyDetail");
}

getList();
</script>
