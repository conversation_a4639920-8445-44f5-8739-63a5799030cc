<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="uploadFileUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :before-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :class="{ hide: fileList.length >= limit }"
    >
      <template #file="{ file }">
        <div>
          <img
            class="el-upload-list__item-thumbnail"
            v-if="type == 'image'"
            :src="file.url"
            alt="图片预览图"
          />
          <img
            class="el-upload-list__item-thumbnail"
            v-else
            :src="
              size === 'small' ? defaultSmallFileImage : defaultLargeFileImage
            "
            alt="文件预览图"
          />
          <p v-if="type == 'file'" class="el-upload-list__item-filename">
            {{ getFileName(file.name) }}
          </p>
          <span class="el-upload-list__item-actions">
            <span
              v-if="size == 'large' && type == 'image'"
              class="el-upload-list__large_item-preview"
              @click="handlePreview(file)"
            >
              <el-icon><View /></el-icon><span>预览</span>
            </span>
            <span
              :class="[
                size === 'large'
                  ? 'el-upload-list__large_item-reselect'
                  : 'el-upload-list__small_item-reselect',
              ]"
              @click="handleReselect(file)"
            >
              <el-icon><upload /></el-icon><span>重新上传</span>
            </span>
            <span
              class="el-upload-list_item-delete"
              @click="handleDelete(file)"
            >
              <el-icon :size="18"><Close /></el-icon>
            </span>
          </span>
        </div>
      </template>
      <div ref="uploadBtn" class="upload-smallImg-tip" v-if="size == 'small'">
        <el-icon><upload-filled /></el-icon>
        <span>上传{{ fileTypeText.value }}</span>
      </div>
      <div ref="uploadBtn" class="upload-largeFile-tip" v-else>
        <el-icon :size="30" class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          <p>将{{ fileTypeText.value }}拖到此处,或<span>点击上传</span></p>
        </div>
      </div>
    </el-upload>
    <el-image-viewer
      v-if="showPreview"
      :url-list="previewList"
      show-progress
      @close="showPreview = false"
    />
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import defaultSmallFileImage from "@/assets/images/default-file.png";
import defaultLargeFileImage from "@/assets/images/default-file2.png";
const props = defineProps({
  modelValue: [String, Array],
  type: {
    type: String,
    default: "image",
  },
  size: {
    type: String,
    default: "small",
  },
  // 文件数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型
  fileType: {
    type: Array,
    default: () => [],
  },
  gap: {
    type: [Number, String],
    default: 25,
  },
});
const realGap = computed(() => {
  if (typeof props.gap === "number") {
    return `${props.gap}px`;
  }
  return props.gap;
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();

const showPreview = ref(false);
const previewList = ref([]);

// 定义文件宽高限制
const uploadWidth = computed(() => (props.size == "small" ? "100px" : "260px"));
const uploadHeight = computed(() =>
  props.size == "small" ? "100px" : "144px"
);

const fileTypeText = computed(() => (props.type === "image" ? "图片" : "文件"));


const uploadBtn = ref(null);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(
  "https://m1.apifoxmock.com/m1/6194657-5887230-default" + "/file/upload"
); // 上传的文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);

const replacingFile = ref(null);

/* watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",");
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          item = { name: item, url: item };
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
); */

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1);
  } else {
    return name;
  }
}

// 上传前loading加载
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split(".");
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy.$modal.msgError(
        `${fileTypeText.value}格式不正确，请上传${props.fileType.join(
          "/"
        )}格式${fileTypeText.value}!`
      );
      return false;
    }
  }
  // 校检文件名是否包含特殊字符
  if (file.name.includes(",")) {
    proxy.$modal.msgError(`${fileTypeText.value}名不正确，不能包含英文逗号!`);
    return false;
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(
        `上传${fileTypeText.value}大小不能超过 ${props.fileSize} MB!`
      );
      return false;
    }
  }
  proxy.$modal.loading(`正在上传${fileTypeText.value}，请稍候...`);
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传${fileTypeText.value}不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    const newFile = { name: res.data.url, url: res.data.url };
    //如果是重新选择文件， 则替换之前的文件位置
    if (replacingFile.value) {
      fileList.value.splice(replacingFile.value.originalIndex, 0, newFile);
      replacingFile.value = null;
    } else {
      fileList.value.push(newFile);
    }
    uploadedSuccessfully();
  } else {
    if (replacingFile.value) {
      fileList.value.splice(replacingFile.value.originalIndex, 0, replacingFile.value.originalFile);
      replacingFile.value = null;
    } else {
      proxy.$modal.msgError(res.msg);
      proxy.$refs.imageUpload.handleRemove(file);
      
    }
    uploadedSuccessfully();
  }
}

function handlePreview(file) {
  previewList.value = [file.url];
  showPreview.value = true;
}
function handleReselect(file) {
  const fileIndex = fileList.value.findIndex((f) => f.name === file.name);
  if (fileIndex !== -1) {
    // 临时移除文件，为重新上传腾出空间
    const tempRemovedFile = fileList.value.splice(fileIndex, 1)[0];

    // 2. 更新modelValue
    emit("update:modelValue", listToString(fileList.value));

    // 3. 标记当前为重新上传模式
    replacingFile.value = {
      originalFile: tempRemovedFile,
      originalIndex: fileIndex,
    };

    // 4. 触发文件选择
    proxy.$refs.uploadBtn.click();
  }
}
function handleDelete(file) {
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  if (findex > -1) {
    fileList.value.splice(findex, 1);
    emit("update:modelValue", listToString(fileList.value));
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
    emit("update:modelValue", listToString(fileList.value));
    proxy.$modal.closeLoading();
}

// 上传失败
function handleUploadError() {
  // 显示上传失败的模态框
  proxy.$modal.msgError(`上传${fileTypeText.value}失败`);
  proxy.$modal.closeLoading();
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
</script>

<style scoped lang="scss">
:deep(.hide .el-upload--picture-card) {
  display: none;
}
.upload-smallImg-tip {
  display: flex;
  align-items: center;
  span {
    font-size: 13px;
    margin-left: 5px;
  }
}
.upload-largeFile-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  .el-upload__text {
    margin-top: 20px;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    letter-spacing: 1.5px;
    p {
      margin: 0px;
    }
    span {
      color: #409eff;
    }
  }
}
.el-upload-list__item-actions {
  position: absolute;
  border-radius: 6px;
  span {
    display: none;
  }
}
.el-upload-list__large_item-preview,
.el-upload-list__large_item-reselect {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40%;
  span {
    margin-top: 10px;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
  }
}
.el-upload-list__small_item-reselect {
  display: flex;
  align-items: center;
  cursor: pointer;
  span {
    margin-left: 3px;
    font-size: 14px;
  }
}
.el-upload-list_item-delete {
  position: absolute;
  right: 5px;
  top: 5px;
  cursor: pointer;
}
:deep(.el-upload--picture-card) {
  width: v-bind(uploadWidth);
  height: v-bind(uploadHeight);
  gap: v-bind(realGap);
  background-color: #f2f2f2;
  border-color: none;
}
:deep(.el-upload-list--picture-card){
 gap: v-bind(realGap);
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: v-bind(uploadWidth);
  height: v-bind(uploadHeight);
  margin: 0px;
  overflow: initial;
  .el-upload-list__item-thumbnail {
    border-radius: 6px;
  }
  .el-upload-list__item-filename {
    margin: 0px;
    width: v-bind(uploadWidth);
    color: #409eff;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  div {
    width: 100%;
  }
  img {
    object-fit: cover;
  }
}
</style>
