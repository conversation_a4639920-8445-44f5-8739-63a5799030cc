import request from '@/utils/requestMock'

// 查询理赔申请列表
export function getInsuredList(data) {
  return request({
    url: '/getInsured/apply/list',
    method: 'get',
    params: data,
  })
}
// 理赔申请详情
export function getInsuredInfo(data) {
    return request({
      url: '/getInsured/apply/infoById',
      method: 'get',
      params: data,
    })
  }

  //获取授权书审核列表
  export function getAuthorizationList(data) {
    return request({
      url: '/getInsured/authorization/list',
      method: 'get',
      params: data,
    })
  }

  // 理赔申请详情
export function getAuthorizationInfo(data) {
  return request({
    url: '/getInsured/Authorization/infoById',
    method: 'get',
    params: data,
  })
}