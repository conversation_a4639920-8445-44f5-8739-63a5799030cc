<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="150px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="数据接入时间范围" prop="entryTimeRange">
            <el-date-picker v-model="dateRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被查询人姓名" prop="insuredName">
            <el-input v-model="queryParams.insuredName" placeholder="被查询人姓名" clearable @keyup.enter="handleQuery" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被查询人证件类型" prop="insuredIdType">
            <el-select v-model="queryParams.insuredIdType" placeholder="请选择被查询人证件类型" clearable style="width: 100%">
              <el-option label="身份证/护照/其他证件" value="身份证/护照/其他证件" />
            </el-select>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="被查询人证件号码" prop="insuredIdNumber">
            <el-input v-model="queryParams.insuredIdNumber" placeholder="请输入被查询人证件号码" clearable @keyup.enter="handleQuery" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="医疗机构名称" prop="hospitalProvince">
            <el-select v-model="queryParams.hospitalProvince" placeholder="请选择医疗机构名称" clearable style="width: 100%">
              <el-option label="请输入医疗机构名称" value="请输入医疗机构名称" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="就诊入院时间范围" prop="appointmentTime">
            <el-date-picker v-model="appointmentRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="就诊流水号" prop="visitNumber">
            <el-input v-model="queryParams.visitNumber" placeholder="请输入就诊流水号" clearable @keyup.enter="handleQuery" />
          </el-form-item>
        </el-col>
        <el-col   style="text-align: right"   :span="16">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>


    <el-table v-loading="loading" :data="testDataList" @selection-change="handleSelectionChange">
      <el-table-column fixed label="序号" align="center" prop="index" width="60" />
      <el-table-column label="数据接入时间" align="center" prop="registerTime"  />
      <el-table-column label="被查询人姓名" align="center" prop="insuredName" />
      <el-table-column label="被查询人证件类型" align="center" prop="insuredIdType" />
      <el-table-column label="被查询人证件号码" align="center" prop="insuredIdNumber" />
      <el-table-column label="医疗机构名称" align="center" prop="hospitalName" />
      <el-table-column label="就诊入院时间" align="center" prop="visitTime"  />
      <el-table-column label="就诊出院时间" align="center" prop="visitEndTime"  />
      <el-table-column label="就诊流水号" align="center" prop="visitNumber" />
        <el-table-column label="操作">
            <template #default="scope">
                <el-button link type="primary" icon="View" @click="handleView(scope.row, scope.index)">查看</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script setup name="TestData">
// import { listTestData } from "@/api/medical/testData";

const { proxy } = getCurrentInstance()

const testDataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const total = ref(0)
const dateRange = ref([])
const appointmentRange = ref([])

// 模拟数据
const mockData = [
  {
    id: 1,
    index: 1,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 2,
    index: 2,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 3,
    index: 3,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 4,
    index: 4,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 5,
    index: 5,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 6,
    index: 6,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 7,
    index: 7,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 8,
    index: 8,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 9,
    index: 9,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  },
  {
    id: 10,
    index: 10,
    registerTime: '2024-09-01 16:53:19',
    insuredName: '王磊',
    insuredIdType: '居民身份证',
    insuredIdNumber: '230504851584684144',
    hospitalProvince: '门诊',
    hospitalName: '联勤保障部队医院',
    visitTime: '2024-09-01',
    visitEndTime: '2024-09-01',
    visitNumber: 'OP1819727'
  }
]

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    insuredName: undefined,
    insuredIdType: undefined,
    insuredIdNumber: undefined,
    hospitalProvince: undefined,
    hospitalName: undefined,
    visitType: undefined,
    visitNumber: undefined
  }
})

const { queryParams } = toRefs(data)

/** 查询检验数据列表 */
function getList() {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    testDataList.value = mockData
    total.value = mockData.length
    loading.value = false
  }, 300)

  // 实际API调用（目前注释掉）
  // 使用dateRange和appointmentRange进行日期范围过滤
  // const params = {
  //   ...queryParams.value,
  //   beginTime: dateRange.value && dateRange.value.length > 0 ? dateRange.value[0] : undefined,
  //   endTime: dateRange.value && dateRange.value.length > 0 ? dateRange.value[1] : undefined,
  //   beginAppointmentTime: appointmentRange.value && appointmentRange.value.length > 0 ? appointmentRange.value[0] : undefined,
  //   endAppointmentTime: appointmentRange.value && appointmentRange.value.length > 0 ? appointmentRange.value[1] : undefined
  // }
  // listTestData(params).then(response => {
  //   testDataList.value = response.rows;
  //   total.value = response.total;
  //   loading.value = false;
  // });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  appointmentRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
}

// 初始化数据
getList()
</script>

<style lang="scss" scoped>
.text-center {
  text-align: center;
}
</style>
