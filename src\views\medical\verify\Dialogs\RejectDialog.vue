<template>
  <el-dialog
    v-model="dialogVisible"
    title="驳回备注"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      status-icon
    >
      <el-form-item label="备注：" prop="remark" required>
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="5"
          maxlength="200"
          show-word-limit
          placeholder="请输入驳回原因"
        />
      </el-form-item>
      <el-form-item label="附件图片：">
        <file-upload
          v-model="form.fileList"
          :limit="6"
          :fileType="['png', 'jpg']"
        ></file-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();
const dialogVisible = defineModel("modelValue", {
  type: Boolean,
  required: true,
  default: false,
});
const emit = defineEmits(["confirm"]);

const formRef = ref();
const form = reactive({
  remark: "",
  fileList: [],
});
const rules = {
  remark: [
    { required: true, message: "请输入驳回原因", trigger: "blur" },
    { max: 200, message: "最多200字", trigger: "blur" },
  ],
};

const handleCancel = () => {
  // 弹出确认提示框，
  proxy.$modal
    .confirm("是否取消备注，并清空当前页面信息？")
    .then(() => {
      // 清空表单信息
      form.remark = "";
      form.fileList = [];
      dialogVisible.value = false;
    })
    .catch(() => {});
};

// 确定
const handleConfirm = () => {
  formRef.value.validate((valid) => {
    if (!valid) {
      ElMessage.error("请填写完整信息");
      return;
    }
    // 调用接口并清除数据

    // 关闭弹窗并清除数据
    form.remark = "";
    form.fileList = [];

    // 触发父组件事件，并关闭弹窗
    emit("confirm");
    dialogVisible.value = false;
  });
};
</script>

<style scoped></style>
