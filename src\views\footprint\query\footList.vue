<template>
  <div class="app-container">
    <div class="foot-search">
      <el-form :model="queryParams" ref="queryRef" label-width="150px">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="医疗机构名称" prop="medicalName">
              <el-select
                v-model="queryParams.medicalName"
                multiple
                filterable
                placeholder="请选择医疗机构名称"
                style="width: 100%"
                clearable
                maxlength="50"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="dict in medicalNameList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="诊断名称" prop="diagnosisName">
              <el-select
                v-model="queryParams.diagnosisName"
                placeholder="请选择诊断名称"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="dict in diagnosisNames"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="诊断类别" prop="diagnosisType">
              <el-select
                v-model="queryParams.diagnosisType"
                placeholder="请选择诊断类别"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="dict in diagnosisTypes"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="就诊/出入院时间范围" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" style="text-align: right">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >查询</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button icon="Upload" @click="handleExport">导出</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      :data="footlistData"
      :span-method="tableSpanMethod"
      @cell-mouse-enter="cellMouseEnter"
      @cell-mouse-leave="cellMouseLeave"
      :row-style="{ height: '45px' }"
    >
      <el-table-column label="时间轴" align="center" class-name="timeline-col">
        <template #default="{ row, $index }">
          <div class="timeline-cell">
            <div class="timeline-date-row">
              <span class="timeline-date" :class="{ active: row.isActive }">{{
                row.date
              }}</span>
            </div>
          </div>
          <span class="timeline-dot" :class="{ active: row.isActive }"></span>
          <span class="timeline-line" :class="{ active: row.isActive }"></span>
        </template>
      </el-table-column>
      <el-table-column
        label="患者姓名"
        align="center"
        prop="name"
        width="150"
      />
      <el-table-column label="证件类型" align="center" prop="cardType" />
      <el-table-column label="证件号码" align="center" prop="cardNumber" />
      <el-table-column label="医疗机构名称" align="center" prop="medicalName" />
      <el-table-column label="就诊类别" align="center" prop="diagnosisType" />
      <el-table-column label="诊断名称" align="center" prop="diagnosisName" />
      <el-table-column
        label="操作"
        width="100"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup name="Verify">
import { listPatient } from "@/api/footprint/query";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const verifyList = ref([]);
const loading = ref(true);
const total = ref(0);

// 医疗结构名称
const medicalNameList = ref([
  {
    label: "天津中医院",
    value: "1",
  },
  {
    label: "天津一中心医院",
    value: "2",
  },
]);

// 诊断名称下拉列表
const diagnosisNames = ref([
  {
    label: "感冒",
    value: "1",
  },
  {
    label: "发烧",
    value: "2",
  },
]);

// 诊断类别下拉列表
const diagnosisTypes = ref([
  {
    label: "门诊",
    value: "1",
  },
  {
    label: "住院",
    value: "2",
  },
  {
    label: "急诊",
    value: "3",
  },
]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    medicalName: undefined,
    diagnosisName: undefined,
    diagnosisType: undefined,
  },
  dateRange: [],
});
const { queryParams, dateRange } = toRefs(data);

// 表格数据
const footlistData = ref([
  {
    date: "08/08 - 08/12",
    name: "张三",
    cardNumber: "123456789012345678",
    cardType: "身份证",
    medicalName: "天津一中心医院",
    diagnosisType: "门诊",
    diagnosisName: "感冒",
    isActive: false,
  },
  {
    date: "08/08 - 08/12",
    name: "张三",
    cardNumber: "123456789012345678",
    cardType: "身份证",
    medicalName: "天津中医院",
    diagnosisType: "住院",
    diagnosisName: "发烧",
  },
  {
    date: "08/08 - 08/12",
    name: "张三",
    cardNumber: "123456789012345678",
    cardType: "身份证",
    medicalName: "天津一中心医院",
    diagnosisType: "急诊",
    diagnosisName: "感冒",
  },
  {
    date: "06/16 - 07/07",
    name: "张三",
    cardNumber: "123456789012345678",
    cardType: "身份证",
    medicalName: "天津中医院",
    diagnosisType: "门诊",
    diagnosisName: "发烧",
  },
  {
    date: "06/16 - 07/07",
    name: "张三",
    cardNumber: "123456789012345678",
    cardType: "身份证",
    medicalName: "天津一中心医院",
    diagnosisType: "住院",
    diagnosisName: "感冒",
  },
  {
    date: "03/05 - 05/05",
    name: "张三",
    cardNumber: "123456789012345678",
    cardType: "身份证",
    medicalName: "天津一中心医院",
    diagnosisType: "急诊",
    diagnosisName: "发烧",
  },
]);

/** 查询岗位列表 */
function getList() {
  loading.value = true;
  listPatient(queryParams.value).then((response) => {
    verifyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 表格合并行或列 */
function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (columnIndex === 0) {
    // 时间轴列
    // 找到当前行的date，统计有多少行是同一个date
    const date = row.date;
    const firstRowIndex = footlistData.value.findIndex(
      (item) => item.date === date
    );
    const count = footlistData.value.filter(
      (item) => item.date === date
    ).length;
    if (rowIndex === firstRowIndex) {
      return [count, 1]; // 合并count行
    } else {
      return [0, 0]; // 其余行隐藏
    }
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.$modal.msgSuccess("导出成功");
  // proxy.download(
  //   "/footprint/patient/export",
  //   queryParams.value,
  //   "患者足迹信息列表.xls"
  // );
}

function handleDetail(row) {
  proxy.$router.push("/footprint/footdetail");
}

function cellMouseEnter(row, column, cell, event) {
  // 找到当前行的日期
  const currentDate = row.date;
  // 遍历所有行，将相同日期的行设置为高亮
  footlistData.value.forEach((item) => {
    if (item.date === currentDate) {
      item.isActive = true;
    }
  });
}

function cellMouseLeave(row, column, cell, event) {
  // 找到当前行的日期
  const currentDate = row.date;
  // 遍历所有行，取消高亮
  footlistData.value.forEach((item) => {
    if (item.date === currentDate) {
      item.isActive = false;
    }
  });
}

getList();
</script>

<style lang="scss" scoped>
.app-container {
  background-color: transparent;
  padding: 0px;
  .foot-search {
    padding: 20px 20px 0px;
    background-color: #fff;
    margin-bottom: 20px;
  }
  .el-table {
    min-height: calc(100vh - 264px);
  }
}
:deep(.el-table__body) {
  .timeline-col {
    border-right: 1px solid #e0e0e0;
    z-index: 9;
    padding: 0px;
    background-color:  #fff  !important;
    vertical-align: top !important;
    border-bottom: 0px;
    .timeline-cell {
      width: 100%;
      line-height: 45px;
      position: relative;
      padding-left: 16px;
    }
    .timeline-date {
      transition: all 0.3s;
      &.active {
        color: #409eff;
        font-weight: bold;
      }
    }
    .timeline-dot {
      position: absolute;
      top: 16.5px;
      right: -6px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #e0e0e0;
      z-index: 999;
      &.active {
        background: #409eff;
      }
    }
    .timeline-line {
      width: 30px;
      height: 2px;
      background: #e0e0e0;
      position: absolute;
      right: 0px;
      top: 22.5px;
      z-index: 1;
      &.active {
        background: #409eff;
      }
    }
  }
  tr:hover > td.el-table__cell{
    background-color: #ebf5ff;
  }
  // 表格最后一行 时间线列加底部边框
  .el-table__row:last-child{
    .timeline-col {
      border-bottom: 1px solid #ebeef5;
    }
  }
}
</style>
