<template>
  <div class="container">
    <!-- 左侧导航菜单 -->
    <el-menu
      class="left-menu"
      mode="vertical"
      :default-openeds="['1', '2', '3', '4', '5', '6', '7', '8']"
      :default-active="activeMenuIndex"
      background-color="#f8f9fa"
      text-color="#333"
      active-text-color="#2d8cf0"
    >
      <!-- 被查询人个人信息 -->
      <el-sub-menu
        index="1"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['personal-info'].includes(activeMenuIndex),
            }"
          >
            被查询人个人信息列表
          </div>
        </template>
        <el-menu-item
          index="personal-info"
          @click="scrollToAnchor('personal-info')"
          >被查询人个人信息</el-menu-item
        >
      </el-sub-menu>

      <!-- 基本机构信息 -->
      <el-sub-menu
        index="2"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['institution-info'].includes(
                activeMenuIndex
              ),
            }"
          >
            基本机构信息列表
          </div>
        </template>
        <el-menu-item
          index="institution-info"
          @click="scrollToAnchor('institution-info')"
          >基本机构信息（一）</el-menu-item
        >
      </el-sub-menu>

      <!-- 基本就诊信息 -->
      <el-sub-menu
        index="3"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': [
                'medical-info-1',
                'medical-info-2',
              ].includes(activeMenuIndex),
            }"
          >
            基本就诊信息列表
          </div>
        </template>
        <el-menu-item
          index="medical-info-1"
          @click="scrollToAnchor('medical-info-1')"
          >基本就诊信息（一）</el-menu-item
        >
        <el-menu-item
          index="medical-info-2"
          @click="scrollToAnchor('medical-info-2')"
          >基本就诊信息（二）</el-menu-item
        >
      </el-sub-menu>

      <!-- 诊断信息 -->
      <el-sub-menu
        index="4"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['diagnosis-1', 'diagnosis-2'].includes(
                activeMenuIndex
              ),
            }"
          >
            诊断信息列表
          </div>
        </template>
        <el-menu-item index="diagnosis-1" @click="scrollToAnchor('diagnosis-1')"
          >诊断信息（一）</el-menu-item
        >
        <el-menu-item index="diagnosis-2" @click="scrollToAnchor('diagnosis-2')"
          >诊断信息（二）</el-menu-item
        >
      </el-sub-menu>

      <!-- 结算信息 -->
      <el-sub-menu
        index="5"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['settlement-1'].includes(activeMenuIndex),
            }"
          >
            结算信息列表
          </div>
        </template>
        <el-menu-item
          index="settlement-1"
          @click="scrollToAnchor('settlement-1')"
          >结算信息（一）</el-menu-item
        >
      </el-sub-menu>

      <!-- 费用明细信息 -->
      <el-sub-menu
        index="6"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['expense-1', 'expense-2'].includes(
                activeMenuIndex
              ),
            }"
          >
            费用明细信息列表
          </div>
        </template>
        <el-menu-item index="expense-1" @click="scrollToAnchor('expense-1')"
          >结算费用明细（一）</el-menu-item
        >
        <el-menu-item index="expense-2" @click="scrollToAnchor('expense-2')"
          >结算费用明细（二）</el-menu-item
        >
      </el-sub-menu>

      <!-- 票据信息 -->
      <el-sub-menu
        index="7"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['invoice-1'].includes(activeMenuIndex),
            }"
          >
            票据信息列表
          </div>
        </template>
        <el-menu-item index="invoice-1" @click="scrollToAnchor('invoice-1')"
          >票据信息（一）</el-menu-item
        >
      </el-sub-menu>

      <!-- 出院小结信息 -->
      <el-sub-menu
        index="8"
        :expand-close-icon="CaretRight"
        :expand-open-icon="CaretBottom"
      >
        <template #title>
          <div
            class="sub-menu-title"
            :class="{
              'select-title-color': ['discharge-1'].includes(activeMenuIndex),
            }"
          >
            出院小结信息列表
          </div>
        </template>
        <el-menu-item index="discharge-1" @click="scrollToAnchor('discharge-1')"
          >出院小结信息（一）</el-menu-item
        >
      </el-sub-menu>
    </el-menu>

    <!-- 右侧内容区域 -->
    <div ref="contentContainer" class="right-content" @scroll="handleScroll">
      <!-- 被查询人个人信息 -->
      <div id="personal-info" class="content-section">
        <CardHeader title="被查询人个人信息" />
        <!-- <div class="info-header">
          <img style="height: 30px" src="./collection-tag.png" alt="" />
          <h3 class="section-title">被查询人个人信息</h3>
        </div> -->
        <div class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">被查询人个人信息</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>被查询人姓名：宁涛</p>
              <p>被查询人证件类型：居民身份证</p>
            </el-col>
            <el-col :span="12">
              <p>被查询人性别：男</p>
              <p>被查询人证件号：120102199908092121</p>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 基本机构信息 -->
      <div id="institution-info" class="content-section">
        <CardHeader title="基本机构信息列表" />
        <div class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">基本机构信息</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>保险机构编码：601318‌</p>
              <p>理赔证件号：120102199908092121</p>
              <p>医疗机构名称：医科大学总医院</p>
            </el-col>
            <el-col :span="12">
              <p>保险机构名称：平安保险</p>
              <p>医疗机构代码：H12010100450 </p>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 基本就诊信息（一） -->
      <div class="content-section">
        <CardHeader title="基本就诊信息列表" />
        <div id="medical-info-1" class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">基本就诊信息（一）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>就诊流水号：OP1911195727</p>
              <p>就诊科室名称：骨科</p>
              <p>医保险种类型：职工基本医疗保险</p>
              <p>就诊/出院时间：2025-06-07 09:16</p>
            </el-col>
            <el-col :span="12">
              <p>就诊科室代码：A01</p>
              <p>就诊类别：住院</p>
              <p>就诊/入院时间：2025-06-12 12:36</p>
            </el-col>
          </el-row>
        </div>
        <div id="medical-info-2" class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">基本就诊信息（二）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>就诊流水号：OP1911195728</p>
              <p>就诊科室名称：骨科</p>
              <p>医保险种类型：职工基本医疗保险</p>
              <p>就诊/出院时间：2025-06-07 09:16</p>
            </el-col>
            <el-col :span="12">
              <p>就诊科室代码：A01</p>
              <p>就诊类别：住院</p>
              <p>就诊/入院时间：2025-06-12 12:36</p>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 诊断信息（一） -->
      <div class="content-section">
        <CardHeader title="诊断信息列表" />
        <div id="diagnosis-1" class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">诊断信息（一）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>诊断代码：OP197</p>
              <p>诊断排序：主要诊断01</p>
            </el-col>
            <el-col :span="12">
              <p>诊断排序：前交叉韧带损伤</p>
            </el-col>
          </el-row>
        </div>
        <div id="diagnosis-2" class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">诊断信息（二）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>诊断代码：OP198</p>
              <p>诊断排序：主要诊断02</p>
            </el-col>
            <el-col :span="12">
              <p>诊断排序：前交叉韧带损伤</p>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 结算信息（一） -->
      <div id="settlement-1" class="content-section">
        <CardHeader title="结算信息列表" />
        <div class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">结算信息（一）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>结算流水号：CP32758714</p>
              <p>自理金额：0.00</p>
              <p>符合医保费用：0.00</p>
              <p>医保起付线：0.00</p>
              <p>统筹基金支付：0.00</p>
              <p>转诊先自付：0.00</p>
              <p>超封顶线自付：0.00</p>
              <p>医院负担：0.00</p>
              <p>大病基金支付：0.00</p>
              <p>大额支付：0.00</p>
            </el-col>
            <el-col :span="12">
              <p>总费用：16098.29</p>
              <p>自费金额：6892.32</p>
              <p>医保基金：0.00</p>
              <p>个人自付：3108.77</p>
              <p>账户支付：3783.55</p>
              <p>统筹分段自付：0.00</p>
              <p>超限价自付金额：0.00</p>
              <p>公务员基金支付：0.00</p>
              <p>民政求助支付：0.00</p>
              <p>其他基金支付：0.00</p>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 费用明细 -->
      <div id="expense-1" class="content-section">
        <CardHeader title="费用结算明细信息列表" />
        <div id="expense-1" class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">费用结算明细信息（一）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>收费类别：OP197</p>
              <p>项目编码：OP19700000001</p>
              <p>项目单价：1682.00</p>
              <p>项目金额：1682.00</p>
            </el-col>
            <el-col :span="12">
              <p>收费项目等级：甲类</p>
              <p>项目名称：MRI</p>
              <p>项目数量：1</p>
            </el-col>
          </el-row>
        </div>
        <div id="expense-2" class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">费用结算明细信息（二）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>收费类别：OP198</p>
              <p>项目编码：OP19800000001</p>
              <p>项目单价：380.00</p>
              <p>项目金额：380.00</p>
            </el-col>
            <el-col :span="12">
              <p>收费项目等级：甲类</p>
              <p>项目名称：超声检查</p>
              <p>项目数量：1</p>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 票据信息（一） -->
      <div id="invoice-1" class="content-section">
        <CardHeader title="票据信息列表" />
        <div class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">票据信息（一）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>票据代码：CP32758714</p>
              <p>票据校验码：000000012100</p>
              <p>
                电子发票地址：https://invoice.exampletax.com/documents/2024/04/INV123456789.pdf
              </p>
            </el-col>
            <el-col :span="12">
              <p>票据号码：000012100020</p>
              <p>开票日期：2025-06-15 09:31</p>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 出院小结信息（一） -->
      <div id="discharge-1" class="content-section">
        <CardHeader title="出院小结中部分信息列表" />
        <div class="info-content">
          <el-row>
            <el-col :span="20">
              <div class="info-title">
                <div class="title">出院小结信息（一）</div>
                <div class="line"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <p>入院日期：2025-06-07 09:16</p>
            </el-col>
            <el-col :span="12">
              <p>出院日期：2025-06-12 12:36</p>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { CaretRight, CaretBottom } from "@element-plus/icons-vue";
import CardHeader from "@/components/CardHeader/index.vue";

const contentContainer = ref(null);
const activeMenuIndex = ref("personal-info"); // 默认激活第一个菜单
const sections = ref([
  { id: "personal-info", top: 0 },
  { id: "institution-info", top: 0 },
  { id: "medical-info-1", top: 0 },
  { id: "medical-info-2", top: 0 },
  { id: "diagnosis-1", top: 0 },
  { id: "diagnosis-2", top: 0 },
  { id: "settlement-1", top: 0 },
  { id: "expense-1", top: 0 },
  { id: "expense-2", top: 0 },
  { id: "invoice-1", top: 0 },
  { id: "discharge-1", top: 0 },
]);

// 计算区块位置
const calculateSectionPositions = () => {
  sections.value.forEach((section) => {
    const el = document.getElementById(section.id);
    if (el) {
      // 相对于容器的顶部位置（容器可能有内边距）
      section.top = el.offsetTop - contentContainer.value.offsetTop;
    }
  });
};

// 滚动处理函数
const handleScroll = () => {
  const scrollTop = contentContainer.value.scrollTop + 50; // 增加50px的检测偏移
  let currentIndex = activeMenuIndex.value;

  // 查找当前滚动位置对应的区块
  for (let i = 0; i < sections.value.length; i++) {
    if (
      scrollTop >= sections.value[i].top &&
      (i === sections.value.length - 1 || scrollTop < sections.value[i + 1].top)
    ) {
      currentIndex = sections.value[i].id;
      console.log("当前激活菜单:", currentIndex);
      activeMenuIndex.value = currentIndex;
      break;
    }
  }

  // 只有当区块确实变化时才更新
  if (currentIndex !== activeMenuIndex.value) {
    activeMenuIndex.value = currentIndex;
  }
};

// 滚动到指定锚点（优化版）
const scrollToAnchor = (anchor) => {
  const el = document.getElementById(anchor);
  if (el) {
    contentContainer.value.scrollTo({
      top: el.offsetTop - contentContainer.value.offsetTop - 20, // 顶部留20px边距
      behavior: "smooth",
    });
  }
};

// 初始化
onMounted(() => {
  calculateSectionPositions();
  window.addEventListener("resize", calculateSectionPositions);
});

// 卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", calculateSectionPositions);
});
</script>

<style scoped lang="scss">
/* 调整所有菜单项的上下间距 */
.el-menu-item {
  height: 30px;
}

:deep(.el-sub-menu__title) {
  height: 40px !important;
}

.container {
  display: flex;
  background: #f0f2f5;

  .left-menu {
    width: 260px;
    padding-left: 16px;
    border-right: 1px solid #e6e9ed;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);

    .sub-menu-title {
      font-weight: 500;
    }

    .select-title-color {
      color: #409eff;
    }

    .unselect-title-color {
      color: #333;
    }
  }

  .right-content {
    flex: 1;
    padding: 0 20px;
    overflow: scroll;
    height: calc(100vh - 124px);
    // background-color: antiquewhite;
    font-size: 14px;
    .content-section {
      background: white;

      .info-content {
        margin-left: 60px;
      }

      .info-title {
        margin-top: 10px;
        display: flex;
        align-items: center;
      }

      .line {
        height: 1px;
        background-color: rgba(238, 238, 238);
        flex: 1;
      }

      .title {
        display: flex;
        align-items: center;
        margin-left: -10px;
        margin-right: 15px;
      }

      .title::before {
        content: "";
        display: inline-block;
        width: 5px;
        height: 15px;
        background: #2d8cf0;
        border-radius: 3px;
        margin-right: 5px;
        margin-top: 1px;
      }
    }
  }
}
</style>
