<template>
  <el-row :gutter="20">
    <!-- 左侧信息区 -->
    <el-col :span="18">
      <div class="leftContent">
        <card-header title="申请信息列表" />
        <div class="infoDetail">
          <h3 class="section-title">申请信息</h3>
          <!-- 申请信息（自定义布局） -->
          <div class="info-area">
            <div class="info-row">
              <div class="info-label">申请时间：</div>
              <div class="info-value">{{ infoData.applyTime }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">医疗机构名称：</div>
              <div class="info-value">{{ infoData.orgName }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">医疗机构代码：</div>
              <div class="info-value">{{ infoData.orgCode }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">联系人姓名：</div>
              <div class="info-value">{{ infoData.contactName }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">联系人手机号：</div>
              <div class="info-value">{{ infoData.contactPhone }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">联系人邮箱：</div>
              <div class="info-value">{{ infoData.contactEmail }}</div>
            </div>
            <div class="info-row" style="align-items: flex-start">
              <div class="info-label">接入阶段：</div>
              <div class="info-value" style="margin: 10px 0px 20px">
                <el-steps
                  style="max-width: 300px"
                  :active="infoData.accessStep"
                  align-center
                  finish-status="success"
                  process-status="finish"
                >
                  <el-step title="申请接入" />
                  <el-step title="数据联调" />
                  <el-step title="接入成功" />
                </el-steps>
              </div>
            </div>
            <div class="info-row">
              <div class="info-label">申报处理所属人：</div>
              <div class="info-value">{{ infoData.handler }}</div>
            </div>
          </div>
          <!-- 申请文件 -->
          <h3 class="section-title">申请文件</h3>
          <div style="margin-bottom: 20px">
            <el-row :gutter="20">
              <el-col :span="6">
                <div style="margin-bottom: 20px">企业营业执照：</div>
                <div class="fileContent">
                  <el-image
                    style="width: 100%; height: 120px"
                    v-if="infoData.licenseUrl"
                    :preview-src-list="imgUrls"
                    :src="infoData.licenseUrl"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :initial-index="0"
                    fit="cover"
                  />
                  <el-icon v-else style="font-size: 60px; color: #409eff"
                    ><document
                  /></el-icon>
                </div>
              </el-col>
              <el-col :span="6">
                <div style="margin-bottom: 20px">数据传输合规承诺书：</div>
                <div class="fileContent">
                  <el-image
                    style="width: 100%; height: 120px"
                    v-if="infoData.promiseUrl"
                    :preview-src-list="imgUrls"
                    :src="infoData.promiseUrl"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :initial-index="1"
                    fit="cover"
                  />
                  <el-icon v-else style="font-size: 60px; color: #409eff"
                    ><document
                  /></el-icon>
                </div>
              </el-col>
            </el-row>
          </div>
          <div style="margin-top: 30px; text-align: center">
            <el-button type="primary" @click="handleAccept">接受申请</el-button>
            <el-button type="danger" @click="handleReject">驳回申请</el-button>
            <el-button
              type="info"
              @click="handleStageConfirm"
              v-if="infoData.accessStep > 0"
              >阶段确认</el-button
            >
          </div>
        </div>
      </div>
    </el-col>
    <!-- 右侧操作记录区 -->
    <el-col :span="6">
      <div class="rightContent">
        <h3 class="section-title">操作记录</h3>
        <el-timeline>
          <el-timeline-item
            color="#409eff"
            timestamp="2024-09-04 16:15:19"
            placement="top"
            v-for="record in records"
            :key="record.time"
          >
            <div class="detail-type">{{ record.type }}</div>
            <div class="detail-item">
              <span class="detail-label">所属人：</span
              ><span class="detail-value">
                {{ record.owner }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">操作人：</span
              ><span class="detail-value">
                {{ record.operator }}
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">操作行为：</span>
              <span class="detail-value">
                {{ record.action }}
              </span>
            </div>
            <div v-if="record.notes" class="detail-item">
              <span class="detail-label">操作备注：</span>
              <span class="detail-value">
                {{ record.notes }}
              </span>
            </div>
            <div
              v-if="record.attachments && record.attachments.length > 0"
              class="detail-item"
            >
              <span class="detail-label">附件图片：</span
              ><span class="detail-value">
                <el-image
                  style="width: 50px; height: 50px; margin-right: 10px"
                  v-for="(item, index) in record.attachments"
                  :preview-src-list="record.attachments"
                  :src="item"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :initial-index="index"
                  :key="item"
                  fit="cover"
                ></el-image>
              </span>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-col>
    <RejectDialog
      v-model="rejectDialogVisible"
      @confirm="handleRejectConfirm"
    ></RejectDialog>
  </el-row>
</template>

<script setup>
import { ElIcon } from "element-plus";
import { Document } from "@element-plus/icons-vue";

import CardHeader from "@/components/CardHeader/index";
import RejectDialog from "./Dialogs/RejectDialog";

const { proxy } = getCurrentInstance();
const infoData = ref({
  applyTime: "2024-09-01 16:15:19",
  orgName: "我是医疗机构名称",
  orgCode: "40135431210210311A1001",
  contactName: "宁涛",
  contactPhone: "13043203774",
  contactEmail: "<EMAIL>",
  accessStep: 0, // 0:申请接入 1:数据联调 2:接入成功
  handler: "张三",
  // 企业营业执照图片和承诺书图片
  licenseUrl:
    "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
  promiseUrl:
    "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
});
// 企业营业执照图片和承诺书图片 计算属性数组
const imgUrls = computed(() => [
  infoData.value.licenseUrl,
  infoData.value.promiseUrl,
]);

// 操作记录数据
const records = ref([
  {
    time: "2024-09-04 16:15:19",
    type: "审核处理",
    owner: "张三",
    operator: "李四",
    action: '确认接入阶段为"接入成功"',
  },
  {
    time: "2024-09-03 16:15:19",
    type: "审核处理",
    owner: "张三",
    operator: "张三",
    action: '确认接入阶段为"数据联调"',
  },
  {
    time: "2024-09-02 16:15:19",
    type: "审核处理",
    owner: "张三",
    operator: "张三",
    action: "接受接入申请",
  },
  {
    time: "2024-09-01 16:15:19",
    type: "审核处理",
    owner: "张三",
    operator: "李四",
    action: "驳回接入申请",
    notes: "我是驳回备注我是驳回备注我是驳回备注我是驳回备注",
    attachments: [
      "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
      "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
      "https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg",
      "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",
    ],
    additionalInfo: "驳回申请后，操作记录的信息",
  },
]);

// 驳回申请弹框控制
const rejectDialogVisible = ref(false);

const handleAccept = () => {
  proxy.$modal
    .confirm("是否确认接收申请？")
    .then(() => {
      // 接收申请逻辑处理
      infoData.value.accessStep = 1;
      proxy.$modal.msgSuccess("接收成功");
    })
    .catch(() => {});
};
const handleReject = () => {
  rejectDialogVisible.value = true;
};
const handleRejectConfirm = () => {
  proxy.$modal.msgSuccess("驳回成功");
};

const handleStageConfirm = () => {
  console.log("阶段确认");
};
</script>

<style lang="scss" scoped>
.leftContent {
  background-color: #ffffff;
  .infoDetail {
    padding: 20px;
    .fileContent {
      display: inline-block;
      background: #f5f7fa;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      width: 100%;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }
    .info-area {
      margin-bottom: 20px;
    }
    .info-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      min-height: 32px;
      font-size: 15px;
      line-height: 1.6;
      margin-bottom: 10px;
    }
    .info-label {
      width: 140px;
      color: #666;
      text-align: left;
      padding-right: 12px;
      font-weight: 500;
    }
    .info-value {
      flex: 1;
      color: #222;
      text-align: left;
      word-break: break-all;
    }
  }
}

.rightContent {
  background-color: #ffffff;
  padding: 20px;
  .el-timeline {
    padding: 0px;
    .detail-type {
      margin-bottom: 5px;
      font-size: 12px;
      font-weight: bold;
    }
    .detail-item {
      display: flex;
      margin-bottom: 10px;
      .detail-label {
        flex: 3;
      }
      .detail-value {
        flex: 7;
      }
    }
  }
}

.section-title {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  font-size: 16px;
  margin: 10px 0px 20px 0px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 18px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
  &.second {
    margin-top: 50px;
  }
}
</style>
