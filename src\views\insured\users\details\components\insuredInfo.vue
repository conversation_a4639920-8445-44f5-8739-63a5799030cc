<template>
  <div class="insuredInfoPage">
    <div class="infoCard">
      <h3 class="section-title">参保人信息</h3>
      <div class="info-area">
        <div class="info-row">
          <div class="info-label">用户姓名：</div>
          <div class="info-value">{{ baseInfo.name || '-' }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">是申请人的什么：</div>
          <div class="info-value">{{ baseInfo.relationship || '-'}}</div>
        </div>
        <div class="info-row" v-if="!isSelf">
          <div class="info-label">证号号：</div>
          <div class="info-value">{{ baseInfo.idCard || '-'}}</div>
        </div>
        <div class="info-row" v-if="!isSelf">
          <div class="info-label">证件有效期：</div>
          <div class="info-value">{{ baseInfo.expires || '-'}}</div>
        </div>
      </div>
    </div>
    <div class="infoCard" v-if="!isSelf">
      <h3 class="section-title">关系证明</h3>
      <div class="info-area">
        <el-image v-for="(img,index) in relationshipImgs"  style="width: 180px; height: 116px;margin-right: 30px;" :initial-index="index" :src="img.url" :preview-src-list="previewImgs"  fit="fill" />
      </div>
    </div>
    <div class="infoCard">
      <h3 class="section-title">医疗数据查询授权书</h3>
      <el-table class="searchBookTable" :data="searchBooksData" style="width: 100%">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column
          prop="applyName"
          label="申请人姓名"
          width="100"
          align="center"
        />
        <el-table-column
          prop="insuredName"
          label="参保人姓名"
          width="100"
          align="center"
        />
        <el-table-column
          prop="authEndTime"
          label="授权截止至"
          align="center"
        />
        <el-table-column
          prop="searchScope"
          label="查询范围"

          align="center"
        />
        <el-table-column prop="signTime" label="签署时间" align="center" />
        <el-table-column prop="authStauts" label="授权书状态" align="center" width="120">
              <template #default="scope">
              <!-- <el-tag v-if="scope.row.authStauts === '1'" type="success"
                >已授权</el-tag
              >
              <el-tag v-if="scope.row.authStauts === '2'" type="danger"
                >已过期</el-tag
              > -->
              <el-tag 
                >{{ scope.row.authStauts }}</el-tag
              > 
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center"  width="120">
          <template #default="scope">
            <el-button text type="primary" @click="openSignFile(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="infoCard">
      <h3 class="section-title">代理赔申请授权书</h3>
      <el-table class="searchBookTable" :data="applyBooksData" style="width: 100%">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column
          prop="applyName"
          label="申请人姓名"
          align="center"
        />
        <el-table-column
          prop="insuredName"
          label="参保人姓名"
          align="center"
        />
        <el-table-column prop="signTime" label="签署时间" align="center" />
        <el-table-column label="操作" align="center"  width="120">
          <template #default="scope">
            <el-button text type="primary" @click="openSignFile(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { getInsuredUserDetail } from "@/api/insured/user";
import { queryUserStatusById } from "@/api/testFlow/index";
const { proxy } = getCurrentInstance();

const  props = defineProps({
  userId: String,
  insuredId:String
});

watch(() => props.insuredId, () => {
  getInsuredUserInfo()
})

const isSelf = computed(() => props.userId === props.insuredId)
const baseInfo = ref({});
const searchBooksData = ref([]);
const applyBooksData = ref([]);

// 关系证明图片列表
const relationshipImgs = ref([]);
const previewImgs = computed(() => relationshipImgs.value.map((item) => item.url))


const getInsuredUserInfo = async () => {
  let res = await getInsuredUserDetail(props.insuredId);
  let statusResponse = await queryUserStatusById();
  if (!res.data) return;
    baseInfo.value = res.data.baseInfo;
    // 查询授权书
    let searchBooksTemp = res.data.searchBooksData;
     searchBooksTemp[0].authStauts = statusResponse.data.shouquanStatusValue || '未授权';
    searchBooksData.value = searchBooksTemp;
    applyBooksData.value = res.data.applyBooksData;
    if (res.data.relationshipImgs) {
      relationshipImgs.value = res.data.relationshipImgs;
    }
};

onMounted(() => {
  getInsuredUserInfo();
});
const openSignFile = (row) => {
  if (!row.signUrl) {
    proxy.$message.error("暂无授权书文件")
    return 
  }
  window.open(row.signUrl)
}
</script>
<style lang="scss" scoped>
.insuredInfoPage {
  height: 100%;
  overflow-y: scroll;
  .infoCard {
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fff;
    .info-area {
      padding-left: 10px;
      margin-top: 20px;
      display: flex;
      flex-wrap: wrap;
    }
    .info-row {
      width: 50%;
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      min-height: 32px;
      font-size: 14px;
      line-height: 1.6;
    }
    .info-label {
      width: 140px;
      color: rgba(0, 0, 0, 0.9);
      text-align: left;
      padding-right: 12px;
      font-weight: 500;
    }
    .info-value {
      flex: 1;
      color: #555555;
      text-align: left;
      word-break: break-all;
    }
  }
  .searchBookTable {
    margin-top: 20px;
  }
}
.section-title {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  font-size: 16px;
  margin: 0px 0px 0px 0px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 18px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
  &.second {
    margin-top: 50px;
  }
}
</style>
