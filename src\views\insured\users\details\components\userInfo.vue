<template>
  <div class="infoDetail">
    <h3 class="section-title">用户信息</h3>
    <div class="info-area">
      <div class="info-row">
        <div class="info-label">用户姓名：</div>
        <div class="info-value">{{ infoData.name }}</div>
      </div>
      <div class="info-row">
        <div class="info-label">注册手机号：</div>
        <div class="info-value">{{ infoData.phone }}</div>
      </div>
      <div class="info-row">
        <div class="info-label">邮箱：</div>
        <div class="info-value">{{ infoData.email }}</div>
      </div>
      <div class="info-row">
        <div class="info-label">联系地址：</div>
        <div class="info-value">{{ infoData.address }}</div>
      </div>
      <div class="info-row">
        <div class="info-label">实名认证状态：</div>
        <div class="info-value">
          <span v-if="infoData.isRealName === 1">已实名</span>
          <span v-else>未实名</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-label">账号状态：</div>
        <div class="info-value">
          <span v-if="infoData.accountStatus === '1'">正常</span>
          <span v-if="infoData.accountStatus === '2'">关闭</span>
          <span v-if="infoData.accountStatus === '3'">已注销</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-label">证件号：</div>
        <div class="info-value">{{ infoData.idcard }}</div>
      </div>
      <div class="info-row">
        <div class="info-label">证件有效期：</div>
        <div class="info-value">{{ infoData.expires }}</div>
      </div>
      <div class="info-row">
        <div class="info-label">身份证正反面：</div>
        <div class="info-imgs">
          <div class="imgItem">
              <file-preview size="large" src="https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg" ></file-preview>
               <span>身份证正面</span>
             </div>
             <div class="imgItem">
              <file-preview size="large" type="image" src="https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg"  ></file-preview>
               <span>身份证反面</span>
             </div>
        </div>
      </div>
    </div>
    <file-upload v-model="images" type="image" size="small"  :limit="4"></file-upload>
    <file-upload v-model="largeImages" type="file" size="large"></file-upload>

  </div>
</template>
<script setup>
import { computed } from "vue";
import { getUserDetail } from "@/api/insured/user";
import { queryUserStatusById } from "@/api/testFlow/index";
const props = defineProps({
  userId: {
    type: String,
    default: () => {
      return "";
    },
  },
});

const infoData = ref({});

const images = ref([
  {
    name: "test.png",
    url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
  },
]);
const largeImages = ref([]);

onMounted(async () => {
  let detailResponse = await getUserDetail(props.userId);
  let statusResponse = await queryUserStatusById();
  infoData.value = {
    ...detailResponse.data,
    ...statusResponse.data,
  };
});
</script>
<style lang="scss" scoped>
.infoDetail {
  padding: 20px;
  height: 100%;
  background-color: #fff;
  .fileContent {
    display: inline-block;
    background: #f5f7fa;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }
  .info-area {
    padding-left: 10px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
  }
  .info-row {
    width: 50%;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    min-height: 32px;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 30px;
  }
  .info-label {
    width: 140px;
    color: rgba(85, 85, 85, 0.9);
    text-align: left;
    padding-right: 12px;
    font-weight: 500;
  }
  .info-value {
    flex: 1;
    color: #000000 100%;
    text-align: left;
    word-break: break-all;
  }
  .info-imgs {
    margin-top: 20px;
    width: 100%;
    display: flex;
    gap: 40px;
    .imgItem {
      margin-right: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span {
        margin-top: 10px;
      }
    }
  }
}

.section-title {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  font-size: 16px;
  margin: 0px 0px 20px 0px;
  padding: 0px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 18px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
  &.second {
    margin-top: 50px;
  }
}
</style>
