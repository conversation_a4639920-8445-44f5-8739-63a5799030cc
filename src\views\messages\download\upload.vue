<template>
  <el-row class="app-container">
    <el-col :span="9">
      <div class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          label-width="120px"
          label-position="left"
          :rules="formRules"
        >
          <h3 class="section-title">上传下载文件</h3>
          <!-- 医院名称 -->
          <el-form-item label="定时上传" prop="uploadSwitch">
            <el-switch v-model="formData.uploadSwitch" active-value="1" inactive-value="0" />
          </el-form-item>
          <el-form-item label="上传时间" prop="uploadTime">
            <el-date-picker v-model="formData.uploadTime"  type="datetime" format="YYYY/MM/DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择上传时间" />
          </el-form-item>
          <el-form-item label="文件名称" prop="fileName">
            <el-input
              v-model="formData.fileName"
              placeholder="请输入文件名称"
            />
          </el-form-item>
          <!-- 文件上传区域 -->
          <el-form-item
                label="企业营业执照"
                label-position="left"
                prop="file"
              >
                <el-upload
                  class="upload-demo"
                  drag
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                  :on-change="handleFileChange"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="upload-tips">
                      <div>文件格式：pdf/jpg/png/docx/doc/xls/xlsx等</div>
                      <div>文件大小：≤10M</div>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
        </el-form>
      </div>
      <div class="footer-actions">
        <div class="right">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </div>
    </el-col>
  </el-row>
</template>
<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/index";

const router = useRouter();
const formRef = ref(null);

const formData = ref({
  uploadSwitch:"0",
  uploadTime: undefined,
  fileName: undefined,
  file: undefined,
});

const formRules = {
  uploadSwitch: [
    { required: true, message: "请选择定时上传开关", trigger: "blur" },
  ],
  uploadTime: [
    { required: true, message: "请选择上传时间", trigger: "blur" },
  ],
  fileName: [
    { required: true, message: "请输入文件名称", trigger: "blur" },
  ],
  file: [
    { required: true, message: "请上传文件", trigger: "blur" },
  ]
};

// form.uploadSwitch 为1时，form.uploadTime为空， 为0时，form.uploadTime为当前时间
watch(() => formData.value.uploadSwitch, (newVal) => {
  if (newVal == '1') {
    formData.value.uploadTime = undefined;
  } else {
    formData.value.uploadTime = formatDate(new Date());
  }
}, { immediate: true });

const beforeUpload = (file) => {
  const isAcceptFormat = [
    "image/jpeg",
    "image/png",
    "application/pdf",
    "application/docx",
    "application/doc",
    "application/xls",
    "application/xlsx",
  ].includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isAcceptFormat) {
    ElMessage.error("只能上传jpg/png/pdf/docx/doc/xls/xlsx格式的文件!");
    return false;
  }
  if (!isLt10M) {
    ElMessage.error("文件大小不能超过10MB!");
    return false;
  }
  return true;
};

const handleFileChange = (file) => {
  formData.value.file = file.raw;
};


const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success("申请提交成功");
      router.go(-1);
      // 这里可以添加提交表单的逻辑
    } else {
      ElMessage.error("请填写完整表单");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
.section-title {
  position: relative;
  width: fit-content;
  font-size: 16px;
  margin:0px 0px 20px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 18px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
}

:deep(.label-top) {
  display: flex;
  flex-direction: column;
}

.upload-demo {
  width: 100%;
}

.upload-tips {
  margin-top: 5px;
  font-size: 12px;
  color: #999;
  div {
    height: 25px;
  }
}

.footer-actions {
  width: 100%;
  margin-top: 30px;
  padding-top: 20px;
  display: flex;
  justify-content: center;
  .el-button {
    margin: 0 10px;
    width: 100px;
  }
}

</style>