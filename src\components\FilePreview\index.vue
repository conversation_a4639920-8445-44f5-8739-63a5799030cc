<template>
  <div class="preview-container">
    <div class="imagePreview" v-for="(fileItem, fileIndex) in fileList">
      <el-image
        ref="imageRef"
        :src="type === 'image' ? fileItem.url : (size === 'small' ? defaultSmallFileImage : defaultLargeFileImage)"

        fit="cover"
        :style="itemStyle"
      >
        <template #error>
          <div class="image-slot">
            <el-icon><picture-filled /></el-icon>
          </div>
        </template>
      </el-image>
      <p v-if="type == 'file' && fileItem.name" class="el-preview-list__item-filename">
        {{ fileItem.name }}
      </p>
      <div class="image-actions" :style="itemStyle">
        <div
          class="preview-icon icon1"
          v-if="size == 'small'"
          @click="handlePreview(fileIndex)"
        >
          <el-icon><ZoomIn /></el-icon>
        </div>
        <div class="preview-icon icon2" v-else @click="handlePreview(fileIndex)">
          <el-icon><View /></el-icon>
          <span>预览</span>
        </div>
      </div>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="previewList"
      :initial-index="previewIndex"
      @close="showViewer = false"
    ></el-image-viewer>
  </div>
</template>

<script setup>
import defaultSmallFileImage from "@/assets/images/default-file.png";
import defaultLargeFileImage from "@/assets/images/default-file2.png";
const props = defineProps({
  src: {
    type: [String, Array],
    default: () => [],
  },
  type: {
    type: String,
    default: "image",
  },
  size: {
    type: String,
    default: "small",
  },
  gap: {
    type: [Number, String],
    default: 10,
  },
});

const fileList = ref([]);
const showViewer = ref(false);
const previewIndex = ref(0);
const previewList = computed(() => {
  return fileList.value.map((item) => item.url);
});

const itemStyle = computed(() => {
  const width = props.size === "small" ? "100px" : "260px";
  const height = props.size === "small" ? "100px" : "144px";
  return `width:${width};height:${height};`;
});

const fileNameWidth = computed(() => props.size === "small" ? "100px" : "260px" )

const realGap = computed(() => {
  if (typeof props.gap === "number") {
    return `${props.gap}px`;
  }
  return props.gap;
});

watch(
  () => props.src,
  (val) => {
    if (val) {
      const list = Array.isArray(val) ? val : props.src.split(",");
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          item = {
            url: item,
            name: item,
          };
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

const handlePreview = (index) => {
  if (props.type === "image") {
    showViewer.value = true;
    previewIndex.value = index;
  } else if (props.type === "file") {
    window.open(fileList.value[index].url, "_blank");
  }
};
</script>

<style lang="scss" scoped>
.preview-container {
  display: flex;
  gap: v-bind(realGap);
  flex-wrap: wrap;
}
.imagePreview {
  position: relative;
  &:hover {
    cursor: pointer;
    .image-actions {
      opacity: 1;
    }
  }
  .el-preview-list__item-filename {
    margin: 0px;
    width: v-bind(fileNameWidth);
    color: #409eff;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .image-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 99;
    cursor: default;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(51, 51, 51, 0.6);
    transition: opacity 0.3s;
    border-radius: 6px;
    .preview-icon {
      cursor: pointer;
    }
    .icon2 {
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 14px;
        margin-top: 5px;
      }
    }
  }
}
.el-image {
  border-radius: 6px;
  background-color: #f2f2f2;
  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
