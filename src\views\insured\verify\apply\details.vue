<template>
  <div class="container">
    <el-tabs v-model="activeName" type="card" class="demo-tabs">
      <el-tab-pane label="详情" name="0"></el-tab-pane>
      <el-tab-pane label="流程图" name="1"></el-tab-pane>
    </el-tabs>

    <div class="insurance-claim-container" v-if="activeName === '0'">
      <!-- 申请人信息 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">申请人信息</span>
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">申请人姓名:</span>
                <span class="info-value">{{ info.applyInfo.applyName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">手机号:</span>
                <span class="info-value">{{ info.applyInfo.phoneNum }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">证件号:</span>
                <span class="info-value">{{ info.applyInfo.idCard }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">证件有效期:</span>
                <span class="info-value">{{ info.applyInfo.youxiaoqi }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">申请人邮箱:</span>
                <span class="info-value">{{ info.applyInfo.email }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">申请人联系地址:</span>
                <span class="info-value">{{ info.applyInfo.address }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
  
      <!-- 参保人信息 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">参保人信息</span>
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">参保人姓名:</span>
                <span class="info-value">{{ info.insuredInfo.insuredName }}</span>
              </div>
            </el-col>
  
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">是申请人的什么:</span>
                <span class="info-value">{{ info.insuredInfo.relationship }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">证件号:</span>
                <span class="info-value">{{ info.insuredInfo.idCard }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">证件有效期:</span>
                <span class="info-value">{{ info.insuredInfo.youxiaoqi }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
  
      <!-- 收款信息 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">收款信息</span>
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">开户银行:</span>
                <span class="info-value">{{ info.shoukuan.bankName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">银行卡号:</span>
                <span class="info-value">{{ info.shoukuan.bankNum }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 申请信息 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">申请信息</span>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="info-label">申请时间:</span>
            <span class="info-value">{{ info.applyData.date }}</span>
          </div>
          <div class="info-item" style="align-items: start">
            <span class="info-label">医疗数据概要:</span>
            <el-table v-loading="loading" :data="info.applyData.list" :row-style="{ height: '45px' }">
              <el-table-column label="序号" type="index" width="50" />
              <el-table-column label="医疗机构" align="center" prop="company" width="200" />
              <el-table-column label="就诊开始时间" align="center" prop="dateStart" width="200" />
              <el-table-column label="就诊结束时间" align="center" prop="dateEnd" />
              <el-table-column label="就诊科室" align="center" prop="keshi"> </el-table-column>
              <el-table-column label="就诊流水号" align="center" prop="id"> </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 理赔材料 -->
      <div class="info-card">
        <div class="card-header">
          <span class="card-title">理赔材料</span>
        </div>
        <div class="info-content">
          <div class="material-images">
            <el-image
              v-for="(item, index) in info.lpCailiao"
              :key="index"
              :src="item"
              fit="cover"
              class="material-image"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="info.lpCailiao"
              :initial-index="index"
            >
            </el-image>
          </div>
        </div>
      </div>

      <!-- 结算信息 -->
      <div class="info-card" v-if="lipeiJLstatus == 5">
        <div class="card-header">
          <span class="card-title">结算信息</span>
        </div>
        <div class="info-content">
          <div class="settlement-item">
            <span class="settlement-label">预赔付金额:</span>
            <span class="settlement-value highlight">6721.59元</span>
            <el-button type="primary" link>查看明细</el-button>
          </div>

          <div class="settlement-item">
            <span class="settlement-label">结算通知书:</span>
            <span class="settlement-value">XXXXXXXX结案通知书.pdf</span>
            <el-button type="primary" link>查看</el-button>
          </div>

          <div class="settlement-item">
            <span class="settlement-label">拒绝通知书:</span>
            <span class="settlement-value">XXXXXXXX拒赔通知书.pdf</span>
            <el-button type="primary" link>查看</el-button>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="action-buttons">
        <el-button type="primary" v-if="[1,3,5,7,9,20].includes(lipeiJLstatus)" plain>查看代理赔申请授权书</el-button>
        <el-button type="primary" v-if="[1,20].includes(lipeiJLstatus)" @click="submitToInsurer">提交至保司</el-button>
        <el-button type="danger" v-if="[1,20].includes(lipeiJLstatus)" @click="dialogFormVisible = true">驳回申请</el-button>
      </div>
    </div>
    <logic-flow v-if="activeName === '1'"></logic-flow>
    <el-dialog v-model="dialogFormVisible" title="驳回备注" width="500" align-center>
      <el-form :model="returnForm" label-width="100px" label-position="right">
        <el-form-item label="驳回原因">
          <el-select v-model="returnForm.region" placeholder="请选择驳回原因">
            <el-option label="理赔文件缺失" value="shanghai" />
          </el-select>
        </el-form-item>
        <el-form-item label="其他原因">
          <el-input v-model="returnForm.name" autocomplete="off" placeholder="请输入其他原因" />
        </el-form-item>
        <el-form-item label="附件图片"> </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="returnThisApply"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getInsuredInfo } from '@/api/insured/verify'
import { ElMessage, ElMessageBox } from 'element-plus'
import { queryUserStatusById, updateUserStatusById } from '@/api/testFlow/index'
const route = useRoute()
const router = useRouter()

import LogicFlow from '@/components/LogicFlow'

const activeName = ref('0')

const loading = ref(false)
const info = ref({
  applyInfo: {},
  insuredInfo: {},
  shoukuan: {},
  applyData: {},
  lpCailiao: []
})
const getInsuredDetails = async () => {
  loading.value = true
  try {
    const res = await getInsuredInfo({ id: 1 }) // 假设传入的ID为1
    if (res.code === 200) {
      info.value = res.data
      console.log(info.value)
    } else {
      console.error('获取理赔详情失败:', res.message)
    }
  } catch (error) {
    console.error('请求错误:', error)
  } finally {
    loading.value = false
  }
}
getInsuredDetails()

const lipeiJLstatus = ref(0)
const lipeiJLstatusValue = ref('')
const getStatus = async () => {
  const res = await queryUserStatusById( )
  if (res.code === 200) {
    lipeiJLstatus.value = res.data.lipeiJLstatus
    lipeiJLstatusValue.value = res.data.lipeiJLstatusValue
  }

}
getStatus()

const dialogFormVisible = ref(false)
const returnForm = ref({
  region: '',
  name: '',
  attachment: ''
})
const returnThisApply = () => {
  ElMessageBox.confirm('确定驳回此申请?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const data = {
      //  驳回
      jiuzhenJLstatus: 30,
      lipeiJLstatus: 30
    }
    updateUserStatusById(data).then(res => {
      if (res.code === 200) {
        ElMessage({
          type: 'success',
          message: '驳回成功'
        })
        dialogFormVisible.value = false
        router.go(-1)
      }
    })
  })
}
const submitToInsurer = () => {
  ElMessageBox.confirm('确定提交至保司?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const data = {
      jiuzhenJLstatus: 3,
      lipeiJLstatus: 3
    }
    updateUserStatusById(data).then(res => {
      if (res.code === 200) {
        ElMessage({
          type: 'success',
          message: '提交成功'
        })
        router.go(-1)
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.container {
  background: #fff;
  padding: 10px;
}
.insurance-claim-container {
  // padding: 20px;
  background-color: white;

  .info-card {
    .card-header {
      padding: 16px;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2d3d;
        position: relative;
        padding-left: 10px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 2px;
          bottom: 2px;
          width: 4px;
          background-color: #409eff;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 0 16px;
      font-size: 14px;

      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        min-height: 28px;

        .info-label {
          font-weight: 500;
          color: #333;
          min-width: 120px;
          margin-right: 10px;
        }

        .info-value {
          color: #666;
          font-weight: 500;
        }
      }

      .sub-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 20px 0 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      }
    }
  }

  .material-images {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .material-image {
      width: 150px;
      height: 100px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;

      :deep(.el-image__inner) {
        object-fit: contain;
      }
    }

    .image-hint {
      color: #909399;
      font-size: 14px;
      margin-bottom: 15px;
      font-style: italic;
    }
  }

  .settlement-item {
    display: flex;
    align-items: center;
    padding: 12px 0;

    .settlement-label {
      font-weight: 500;
      color: #333;
      min-width: 120px;
    }

    .settlement-value {
      color: #666;
      font-weight: 500;
    }

    .el-button {
      margin-left: 10px;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    border-radius: 8px;
  }
}
</style>
