<template>
  <div class="insurance-claim-container">
    <!-- 申请人信息 -->
    <div class="info-card">
      <div class="card-header">
        <span class="card-title">申请人信息</span>
      </div>
      <div class="info-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">申请人姓名:</span>
              <span class="info-value">宁涛</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">手机号:</span>
              <span class="info-value">13029786279</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">证件号:</span>
              <span class="info-value">120102199908092121</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">证件有效期:</span>
              <span class="info-value">2017-09 - 2027-09</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">申请人邮箱:</span>
              <span class="info-value">- -</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">申请人联系地址:</span>
              <span class="info-value">- -</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 参保人信息 -->
    <div class="info-card">
      <div class="card-header">
        <span class="card-title">参保人信息</span>
      </div>
      <div class="info-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">参保人姓名:</span>
              <span class="info-value">宁涛</span>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">是申请人的什么:</span>
              <span class="info-value">本人</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">证件号:</span>
              <span class="info-value">120102199908092121</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">证件有效期:</span>
              <span class="info-value">2017-09 - 2027-09</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 收款信息 -->
    <div class="info-card">
      <div class="card-header">
        <span class="card-title">收款信息</span>
      </div>
      <div class="info-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">开户银行:</span>
              <span class="info-value">渤海银行</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">银行卡号:</span>
              <span class="info-value">6222 2982 1433 786</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 申请信息 -->
    <div class="info-card">
      <div class="card-header">
        <span class="card-title">申请信息</span>
      </div>
      <div class="info-content">
        <div class="info-item">
          <span class="info-label">申请时间:</span>
          <span class="info-value">{{ formatDate(Date()) }}</span>
        </div>
        <div class="info-item" style="align-items: start">
          <span class="info-label">医疗数据概要:</span>
          <dataSummary />
        </div>
      </div>
    </div>

    <!-- 理赔材料 -->
    <div class="info-card">
      <div class="card-header">
        <span class="card-title">理赔材料</span>
      </div>
      <div class="info-content">
        <div class="material-images">
          <el-image
            v-for="(item, index) in materials"
            :key="index"
            :src="item.url"
            fit="cover"
            class="material-image"
            :preview-src-list="previewList"
            :initial-index="index"
          >
            <template #error>
              <div class="image-error">加载失败</div>
            </template>
          </el-image>
        </div>
      </div>
    </div>

    <!-- 结算信息 -->
    <div class="info-card">
      <div class="card-header">
        <span class="card-title">结算信息</span>
      </div>
      <div class="info-content">
        <div class="settlement-item">
          <span class="settlement-label">预赔付金额:</span>
          <span class="settlement-value highlight" v-if="userDataStatus == 7 || userDataStatus == 9">6721.59元</span>
          <el-button type="primary" link v-if="userDataStatus == 7 || userDataStatus == 9" @click="showPreview = true">查看明细</el-button>
        </div>

        <div class="settlement-item">
          <span class="settlement-label">结算通知书:</span>
          <span class="settlement-value">{{ userDataStatus == 9 ? '结案通知书.pdf' : '-'}}</span>
          <el-button type="primary" link v-if="userDataStatus == 9" 
            @click="openLink('https://tjhfd.oss-cn-beijing.aliyuncs.com/tjhfd/upload/image/202507/1753153454184/14a07434c46cc286ca7c9ac6b5455198.pdf')"
          >查看</el-button>
        </div>

        <div class="settlement-item">
          <span class="settlement-label">拒绝通知书:</span>
          <span class="settlement-value">{{ userDataStatus == 60 ? '拒赔通知书.pdf' : '-'}}</span>
          <el-button type="primary" link v-if="userDataStatus == 60"
            @click="openLink('https://tjhfd.oss-cn-beijing.aliyuncs.com/tjhfd/upload/image/202507/1753153482684/8e408c55f2a12073d5b2903b89f1dd88.pdf')"
          >查看</el-button>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <el-button type="primary" v-if="buttonStatus.isShowPayment" @click="showPaymentDialog=true">上传预赔付金额</el-button>
      <el-button type="primary" v-if="buttonStatus.isShowClosing"  @click="showClosingDialog=true">结案</el-button>
      <el-button type="primary" v-if="buttonStatus.isShowAgreement"  plain>查看代理赔申请授权书</el-button>
      <el-button type="danger" v-if="buttonStatus.isShowRefusal"  @click="showRefusalDialog=true">驳回申请</el-button>
      <el-button type="danger" v-if="buttonStatus.isShowReject"  @click="showRejectDialog=true">拒绝理赔</el-button>
    </div>
    <rejectDialog 
      v-model:visible="showRejectDialog" 
      @confirmed="handleDialogResult" />
    <refusalDialog 
      v-model:visible="showRefusalDialog" 
      @confirmed="handleDialogResult" />
    <paymentDialog 
      v-model:visible="showPaymentDialog" 
      @confirmed="handleDialogResult" />
    <closingDialog 
      v-model:visible="showClosingDialog" 
      @confirmed="handleDialogResult" />
    <el-image-viewer
      v-if="showPreview"
      :url-list="imageList"
      show-progress
      :initial-index="0"
      @close="showPreview = false"
    />
  </div>
</template>

<script setup name="Claimaudit">
import { formatDate } from "@/utils/index";
import { queryUserStatusById,updateUserStatusById } from '@/api/testFlow/index'

import dataSummary from "../components/dataSummary.vue";
import rejectDialog from "../components/rejectDialog.vue";
import refusalDialog from "../components/refusalDialog.vue";
import paymentDialog from "../components/paymentDialog.vue";
import closingDialog from "../components/closingDialog";
const showRejectDialog = ref(false)
const showRefusalDialog = ref(false)
const showPaymentDialog = ref(false)
const showClosingDialog = ref(false)
const showPreview = ref(false)
const imageList = ref(['https://tjhfd.oss-cn-beijing.aliyuncs.com/tjhfd/upload/image/202507/1753153292345/d4a0514866899b2bdc7caef0ba0743ee.png'])
const openLink = (link) => {
  window.open(link)
}
const handleDialogResult = (data) => {
  console.log('拒绝理赔:', data);
  // 这里处理拒绝逻辑，例如调用API
  let value = '0'
  switch (data.type) {
    case 'reject':
      value = '60'
      break;
    case 'refusal':
      value = '20'
      break;
    case 'payment':
      value = '5'
      break;
    case 'closing':
      value = '9'
      break;
    default:
      break;
  }
  updateUserStatusById({lipeiJLstatus: value, jiuzhenJLstatus: value}).then(
    () => {
      getUserStatus()
    }
  )
};

const buttonStatus = ref({
  isShowReject: false,
  isShowRefusal: false,
  isShowAgreement: false,
  isShowPayment: false,
  isShowClosing: false
})
// 理赔材料
const materials = ref([
  
]);

// 预览图片列表
const previewList = computed(() => materials.value.map((item) => item.url));

// 记录数据状态用
const userDataStatus = ref('')
const getUserStatus = () => {
  queryUserStatusById().then(
    status => {
      const resStatus = status.data
      userDataStatus.value = resStatus.lipeiJLstatus
        buttonStatus.value.isShowReject = false
        buttonStatus.value.isShowRefusal = false
        buttonStatus.value.isShowAgreement = false
        buttonStatus.value.isShowPayment = false
        buttonStatus.value.isShowClosing = false

      if (resStatus.lipeiJLstatus == 3) {
        buttonStatus.value.isShowReject = true
        buttonStatus.value.isShowRefusal = true
        buttonStatus.value.isShowAgreement = true
        buttonStatus.value.isShowPayment = true
      } else if (resStatus.lipeiJLstatus == 5) {
        buttonStatus.value.isShowAgreement = true
      } else if (resStatus.lipeiJLstatus == 7) {
        buttonStatus.value.isShowAgreement = true
        buttonStatus.value.isShowClosing = true
      } else if (resStatus.lipeiJLstatus == 9) {
        buttonStatus.value.isShowAgreement = true
      } else if (resStatus.lipeiJLstatus == 30) {
      } else if (resStatus.lipeiJLstatus == 40) {
      } else if (resStatus.lipeiJLstatus == 50) {
      } else if (resStatus.lipeiJLstatus == 60) {
      } else {
      }
    }  
  )
}

getUserStatus()
</script>

<style lang="scss" scoped>
.insurance-claim-container {
  padding: 10px;
  background-color: white;

  .info-card {
    .card-header {
      padding: 16px;

      .card-title {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        position: relative;
        padding-left: 10px;

        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 2px;
          bottom: 2px;
          width: 4px;
          background-color: #409eff;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 0 16px;
      font-size: 14px;

      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        min-height: 28px;

        .info-label {
          font-weight: 500;
          color: #333;
          min-width: 120px;
          margin-right: 10px;
        }

        .info-value {
          color: #666;
          font-weight: 500;
        }
      }

      .sub-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 20px 0 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      }
    }
  }

  .material-images {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .material-image {
      width: 150px;
      height: 100px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;

      :deep(.el-image__inner) {
        object-fit: contain;
      }
    }

    .image-hint {
      color: #909399;
      font-size: 14px;
      margin-bottom: 15px;
      font-style: italic;
    }
  }

  .settlement-item {
    display: flex;
    align-items: center;
    padding: 12px 0;

    .settlement-label {
      font-weight: 500;
      color: #333;
      min-width: 120px;
    }

    .settlement-value {
      color: #666;
      font-weight: 500;
    }

    .el-button {
      margin-left: 10px;
    }
  }
}
.action-buttons {
  // position: fixed;
  // bottom: 30px;
  // right: 30px;
  // z-index: 99;
  // background-color: white;
  display: flex;
  flex-direction: row-reverse;
  gap: 10px;
  margin-top: 30px;
  border-radius: 8px;

  .el-button {
    padding: 0 10px;
    font-weight: 500;
    font-size: 14px;
  }
}
</style>