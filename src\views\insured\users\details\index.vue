<template>
  <div class="app-container">
    <div class="leftContainer">
      <el-menu
        default-active="userInfo"
        class="el-menu"
        @select="handleMenuSelect"
      >
        <el-menu-item index="userInfo">用户信息</el-menu-item>
        <el-sub-menu index="insuredData">
          <template #title>
            <span>参保人信息</span>
          </template>
          <el-menu-item
            v-for="(user, index) in insuredUsers"
            :index="`insured_${user.id}`"
            >{{ user.name }}</el-menu-item
          >
        </el-sub-menu>
        <el-menu-item index="records">理赔记录</el-menu-item>
      </el-menu>
    </div>
    <div class="rightContainer">
      <!-- 用户信息组件 -->
      <user-info v-if="activeMenu === 'userInfo'" :user-id="userId"></user-info>

      <!-- 参保人信息组件 -->
      <insured-info
        v-else-if="activeMenu.startsWith('insured_')"
        :insured-id="currentInsuredId"
        :user-id="userId"
      />
      <!-- 理赔记录组件 -->
      <records v-else-if="activeMenu === 'records'" :user-id="userId"/>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
// 引入路由
import { useRoute } from "vue-router";
import UserInfo from "./components/userInfo.vue";
import InsuredInfo from "./components/insuredInfo.vue";
import Records from "./components/records.vue";

const route = useRoute();

// watch监听路由参数获取 userId
const userId = ref("");
watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.userId !== undefined) {
      userId.value = newQuery.userId;
    }
  },
  { immediate: true },
);


const activeMenu = ref("userInfo");
// 参保人信息列表
const insuredUsers = ref([
  {
    name: "宁涛",
    id: "1",
  },
 /*  {
    name: "李四",
    id: "2",
  }, */
]);
// 计算当前选中的参保人ID
const currentInsuredId = computed(() => {
  if (activeMenu.value.startsWith("insured_")) {
    return activeMenu.value.replace("insured_", "");
  }
  return "";
});

const handleMenuSelect = (index) => {
  activeMenu.value = index;
};
</script>
<style scoped lang="scss">
.app-container {
  background: none;
  padding: 0px;
  display: flex;
  gap: 20px;
}
.leftContainer {
  width: 234px;
  flex-shrink: 0;
  padding: 10px;
  background-color: #fff;
  .el-menu {
    height: 100%;
    border-right: none;
    :deep(.el-menu-item) {
      height: 30px;
      line-height: 30px;
      justify-content: center;
      padding: 0px;
      margin-bottom: 8px;
      &:hover {
        background-color: rgba(235, 245, 255, 1) !important;
      }
      &.is-active {
        background-color: rgba(235, 245, 255, 1) !important;
      }
    }
    :deep(.el-sub-menu__title) {
      height: 30px;
      line-height: 30px;
      padding: 0px;
      justify-content: center;
      margin-bottom: 8px;
    }
    :deep(.el-sub-menu.is-active){
      .el-sub-menu__title{
        color: #409EFF!important;
        background-color: #EBF5FF !important;
      }
    }
  }
}
.rightContainer {
  flex: 1;
}
</style>
