<template>
  <div class="cardHeader" :style="{ width, height }">
    <svg-icon icon-class="book-mark" style="height: 30px; width: 20px" />
    <span class="title">{{ title }}</span>
  </div>
</template>
<script setup>
// 父组件传递数据
const props = defineProps({
  title: {
    type: String,
    default: "信息列表",
  },
  icon: {
    type: String,
    default: "book-mark",
  },
  size: {
    type: Number,
    default: 25,
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "45px",
  },
});
</script>
<style lang="scss" scoped>
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: #ebf5ff;
  padding: 5px 15px;
  .title {
    margin-left: 10px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
  }
}
</style>
