<template>
  <div class="log-container">
    <el-timeline>
      <el-timeline-item
        v-for="(log, index) in timelineData"
        :key="index"
        :timestamp="log.timestamp"
        placement="top"
      >
        <!-- 时间轴节点内容 -->
        <div class="log-card">
          <!-- 信息项布局 -->
          <div class="log-item">
            <span class="label">异常数据所属人：</span>
            <span class="content">{{ log.owner }}</span>
          </div>
          
          <div class="log-item">
            <span class="label">审核人：</span>
            <span class="content">{{ log.reviewer }}</span>
          </div>

          <div class="log-item">
            <span class="label">审核项：</span>
            <el-tag 
              type="success" 
              effect="light"
              class="status-tag"
            >
              {{ log.status }}
            </el-tag>
          </div>

          <div class="log-item">
            <span class="label">关联异常编号：</span>
            <div class="related-list">
              <span 
                v-for="(link, idx) in log.relatedExceptions"
                :key="idx"
                class="related-item"
              >
                {{ link }}
                <el-button 
                  type="primary" 
                  link
                  @click="handleViewLink(link)"
                >
                  查看
                </el-button>
              </span>
            </div>
          </div>

          <div class="log-item">
            <span class="label">审核备注：</span>
            <pre class="remark">{{ log.remark }}</pre>
          </div>

          <!-- 附件图片 -->
          <div class="log-item">
            <span class="label">附件图片：</span>
            <div class="image-list">
              <el-image
                v-for="(img, i) in log.attachments"
                :key="i"
                :src="img"
                :preview-src-list="log.attachments"
                :initial-index="i"
                class="attached-image"
                fit="cover"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Picture } from '@element-plus/icons-vue'

// 模拟数据
const timelineData = ref([{
  timestamp: '2024-09-04 16:15:19',
  owner: '张三',
  reviewer: '李四',
  status: '已正常',
  relatedExceptions: [
    'OP191195727250408a01',
    'OP1911195727250408a02',
    'OP1911195727250408a03'
  ],
  remark: `我是审核备注我是审核备注我
是审核备注我是审核备注我是审核备注我是
审核备注我是审核备注`,
  attachments: [
    'https://cdn.example.com/photo1.jpg',
    'https://cdn.example.com/photo2.jpg'
  ]
}])

const handleViewLink = (linkId) => {
  console.log('查看关联异常:', linkId)
}
</script>

<style lang="scss" scoped>
.log-container {
  padding: 20px 0;
  background: white;

  // 自定义时间轴样式
  :deep(.el-timeline) {
    .el-timeline-item__node {
      background-color: #1890ff;
      border-color: #1890ff;
      width: 14px;
      height: 14px;
    }

    .el-timeline-item__tail {
      left: 6px;
      border-color: #1890ff;
    }

    .el-timeline-item__timestamp {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }
  }
  :deep(.div.is-always-shadow) {
    border: none;
    box-shadow: none;
  }

  .log-card {
    margin-bottom: 20px;
    border-radius: 8px;

    // border: 1px solid #e6ebf5;

    .log-item {
      display: flex;
      margin-bottom: 12px;
      line-height: 1.6;

      .label {
        color: #333;
        min-width: 120px;
        text-align: right;
        padding-right: 16px;
        flex-shrink: 0;
      }

      .content {
        color: #333;
        flex-grow: 1;
      }

      .related-list {
        .related-item {
          display: block;
          margin: 4px 0;
          color: #1890ff;

          .el-button {
            padding-left: 8px;
          }
        }
      }

      .remark {
        color: #606266;
        white-space: pre-wrap;
        margin: 0;
        font-family: inherit;
      }

      .image-list {
        display: flex;
        gap: 12px;

        .attached-image {
          width: 120px;
          height: 80px;
          border-radius: 4px;
          border: 1px solid #e6e6e6;

          .image-error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #f5f7fa;
          }
        }
      }
    }
  }

  .status-tag {
    border-color: #e1f3d8;
    color: #67c23a;
  }
}
</style>