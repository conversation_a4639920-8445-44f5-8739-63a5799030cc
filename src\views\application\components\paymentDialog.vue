<template>
  <el-dialog
    v-model="dialogVisible"
    title="拒绝理赔"
    width="460px"
    class="reject-dialog"
  > 
    <!-- 弹窗主体内容 -->
    <div class="dialog-body">
      <!-- 拒赔原因 -->
      <div class="input-item">
        <div class="input-label">          
          <span style="color: #f56c6c;">*</span>
          预赔付金额:</div>
        <el-input
          v-model="rejectReason"
          placeholder="请输入预赔付金额"
          class="reason-input"
          resize="none"
        ></el-input>
      </div>
      
      <!-- 拒赔通知书上传 -->
      <div class="input-item required">
        <div class="input-label">
          <span style="color: #f56c6c;">*</span>
          <span>金额明细:</span>
        </div>
        <div class="upload-container" @click="triggerFileInput">
          <div class="upload-content">
            <el-icon v-if="!uploadedFile" class="upload-icon"><Upload /></el-icon>
            <template v-if="uploadedFile">
              <el-icon class="file-icon"><Document /></el-icon>
              <!-- uploadedFile.name -->
              <div class="file-name">上传成功或<span style="color:#409eff">点击重新上传</span></div>
            </template>
            <div v-else class="upload-text">上传文件</div>
          </div>
          <input 
            ref="fileInput"
            type="file" 
            style="display: none" 
            @change="handleFileChange"
          >
        </div>
      </div>
    </div>
    
    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
      <el-button  @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
    </template>
  </el-dialog>
  
  <!-- 二次确认弹窗 -->
  <el-dialog
    v-model="confirmDialogVisible"
    width="420px"
    class="confirm-dialog"
    append-to-body
  >
    <div class="confirm-content">
      <div class="confirm-message">是否确认此次预赔付金额为6721.59元？</div>
    </div>
  
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmDialogVisible = false">取消</el-button>
        <el-button  type="primary" @click="handleFinalConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Upload, Document } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'confirmed']);

const dialogVisible = ref(props.visible);
const confirmDialogVisible = ref(false);
const rejectReason = ref('');
const uploadedFile = ref(null);
const fileInput = ref(null);

// 点击上传区域
const triggerFileInput = () => {
  fileInput.value?.click();
};

// 文件选择处理
const handleFileChange = (e) => {
  const file = e.target.files[0];
  if (file) {
    uploadedFile.value = file;
  }
};

// 关闭弹窗
const handleCancel = () => {
  dialogVisible.value = false;
};

// 点击确定按钮
const handleConfirm = () => {
  // 检查是否已上传文件
  if (!uploadedFile.value) {
    ElMessage.error('请上传拒赔通知书');
    return;
  }
  
  confirmDialogVisible.value = true;
};

// 最终确认拒绝
const handleFinalConfirm = () => {
  // 创建拒绝信息对象
  const rejectData = {
    type: 'payment',
    reason: rejectReason.value,
    file: uploadedFile.value
  };
  
  // 传递给父组件
  emit('confirmed', rejectData);
  
  // 关闭所有弹窗
  confirmDialogVisible.value = false;
  dialogVisible.value = false;
  
  // 重置表单
  rejectReason.value = '';
  uploadedFile.value = null;
};

// 监听props变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val;
});

// 监听dialog可见状态变化
watch(dialogVisible, (val) => {
  emit('update:visible', val);
});
</script>

<style lang="scss" scoped>
.reject-dialog {
  border-radius: 8px;
  border: 1px solid #409eff;
  
  .dialog-body {
    padding: 20px;
    
    .input-item {
      margin-bottom: 25px;
      display: flex;
      
      &.required .input-label .required-icon {
        color: #f56c6c;
      }
      
      .input-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
        // display: flex;
        min-width: 80px;
        // align-items: center;
      }
      
      .reason-input {
        margin-top: -5px;
       height: 30px;
      }
      
      .upload-container {
        margin-left: 10px;
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        background-color: #dee2e6;
        height: 130px;
        width: 160px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        
        &:hover {
          border-color: #dee2e6;
        }
        
        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          
          .upload-icon {
            font-size: 30px;
            color: #909399;
            margin-bottom: 10px;
          }
          
          .file-icon {
            font-size: 30px;
            color: #409eff;
            margin-bottom: 8px;
          }
          
          .file-name {
            font-size: 12px;
            color: #606266;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .upload-text {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }
}

.confirm-dialog {
  .confirm-content {
    padding: 30px 30px 20px;
    
    .confirm-message {
      font-size: 16px;
      color: #303133;
      text-align: center;
    }
  }
}
</style>