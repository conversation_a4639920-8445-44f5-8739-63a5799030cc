<template>
  <div id="container"></div>
</template>
<script setup>
import LogicFlow from '@logicflow/core'
import '@logicflow/core/dist/index.css'

import { queryUserStatusById, updateUserStatusById } from '@/api/testFlow/index'
const lipeiJLstatus = ref(0)
const lipeiJLstatusValue = ref('')

const hightColor = '#409eff'
const lowColor = '#EBF5FF'
const data = computed(() => {
  return {
    nodes: [
      {
        id: 999,
        type: 'rect',
        x: 100,
        y: 150,
        text: '开始',
        properties: {
          radius: 20,
          style: {
            fill: lipeiJLstatus.value === 999 ? hightColor: generateArray(lipeiJLstatus.value).includes(999) ? lowColor : '#ffffff',
            strokeWidth:generateArray(lipeiJLstatus.value).includes(999) ? 0 : 1,
          }
        },
        
      },
      {
        id: 40,
        type: 'rect',
        x: 300,
        y: 0,
        text: '申请已撤销',
        properties: {
          style: {
            fill:lipeiJLstatus.value === 40 ? hightColor: generateArray(lipeiJLstatus.value).includes(40) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(40) ? 0 : 1
          }
        }
      },
      {
        id: 1,
        type: 'rect',
        x: 300,
        y: 150,
        text: '申请待平台审核',
        properties: {
          style: {
            fill:lipeiJLstatus.value === 1 ? hightColor: generateArray(lipeiJLstatus.value).includes(1) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(1) ? 0 : 1
          }
        }
      },
      {
        id: 2,
        type: 'diamond',
        x: 500,
        y: 150,
        text: '平台审核',
        properties: {
          rx: 80,
          style: {
            fill:lipeiJLstatus.value === 2 ? hightColor: generateArray(lipeiJLstatus.value).includes(2) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(2) ? 0 : 1
          }
        }
      },
      {
        id: 20,
        type: 'rect',
        x: 500,
        y: 0,
        text: '保司驳回申请，待重新审核',
        properties: {
          width: 200,
          style: {
            fill: lipeiJLstatus.value === 20 ? hightColor: generateArray(lipeiJLstatus.value).includes(20) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(20) ? 0 : 1
          }
        }
      },
      {
        id: 30,
        type: 'rect',
        x: 500,
        y: 300,
        text: '申请已驳回给参保人',
        properties: {
          width: 200,
          style: {
            fill: lipeiJLstatus.value === 30 ? hightColor: generateArray(lipeiJLstatus.value).includes(30) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(30) ? 0 : 1
          }
        }
      },
      {
        id: 3,
        type: 'rect',
        x: 740,
        y: 150,
        text: '申请待保司审核',
        properties: {
          width: 150,
          style: {
            fill: lipeiJLstatus.value === 3 ? hightColor: generateArray(lipeiJLstatus.value).includes(3) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(3) ? 0 : 1
          }
        }
      },
      {
        id: 4,
        type: 'diamond',
        x: 950,
        y: 150,
        text: '保险公司审核',
        properties: {
          rx: 80,
          style: {
            fill: lipeiJLstatus.value === 4 ? hightColor: generateArray(lipeiJLstatus.value).includes(4) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(4) ? 0 : 1
          }
        }
      },
      {
        id: 60,
        type: 'rect',
        x: 950,
        y: 300,
        text: '保险公司已拒赔',
        properties: {
          width: 200,
          style: {
            fill: lipeiJLstatus.value === 60 ? hightColor: generateArray(lipeiJLstatus.value).includes(60) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(60) ? 0 : 1
          }
        }
      },
      {
        id: 5,
        type: 'rect',
        x: 1260,
        y: 150,
        text: '预赔付金额待确认',
        properties: {
          width: 150,
          style: {
            fill: lipeiJLstatus.value === 5 ? hightColor: generateArray(lipeiJLstatus.value).includes(5) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(5) ? 0 : 1
          }
        }
      },
      {
        id: 6,
        type: 'diamond',
        x: 1550,
        y: 150,
        text: '参保人是否接受预赔付金额',
        properties: {
          rx: 100,
          style: {
            fill: lipeiJLstatus.value === 6 ? hightColor: generateArray(lipeiJLstatus.value).includes(6) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(6) ? 0 : 1
          }
        }
      },
      {
        id: 50,
        type: 'rect',
        x: 1550,
        y: 300,
        text: '参保人不接受预赔付金额',
        properties: {
          width: 150,
          style: {
            fill: lipeiJLstatus.value === 50 ? hightColor: generateArray(lipeiJLstatus.value).includes(50) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(50) ? 0 : 1
          }
        }
      },
      {
        id: 7,
        type: 'rect',
        x: 1820,
        y: 150,
        text: '参保人已接收预赔付金额',
        properties: {
          width: 150,
          style: {
            fill: lipeiJLstatus.value === 7 ? hightColor: generateArray(lipeiJLstatus.value).includes(7) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(7) ? 0 : 1
          }
        }
      },
      {
        id: 9,
        type: 'rect',
        x: 2100,
        y: 150,
        text: '保司结案',
        properties: {
          width: 150,
          style: {
            fill: lipeiJLstatus.value === 9 ? hightColor: generateArray(lipeiJLstatus.value).includes(9) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(9) ? 0 : 1
          }
        }
      },
      {
        id: 10,
        type: 'rect',
        x: 2300,
        y: 150,
        text: '结束',
        style: {
            fill: lipeiJLstatus.value === 10 ? hightColor: generateArray(lipeiJLstatus.value).includes(10) ? lowColor : '#ffffff',
            strokeWidth: generateArray(lipeiJLstatus.value).includes(10) ? 0 : 1
          }
      }
    ],
    // 边
    edges: [
      {
        type: 'polyline',
        sourceNodeId: 999,
        targetNodeId: 1,
        text: '提交申请'
      },
      {
        type: 'polyline',
        sourceNodeId: 1,
        targetNodeId: 2
        //   text:'申请人提交申请'
      },
      {
        type: 'polyline',
        sourceNodeId: 2,
        targetNodeId: 3,
        text: '通过'
      },
      {
        type: 'polyline',
        sourceNodeId: 3,
        targetNodeId: 4
        //   text:'通过'
      },
      {
        type: 'polyline',
        sourceNodeId: 4,
        targetNodeId: 5,
        text: '通过'
      },
      {
        type: 'polyline',
        sourceNodeId: 5,
        targetNodeId: 6
        //   text: '通过,保司上传预赔付金额'
      },
      {
        type: 'polyline',
        sourceNodeId: 6,
        targetNodeId: 7,
        text: '接受'
      },
      {
        type: 'polyline',
        sourceNodeId: 7,
        targetNodeId: 9
        //   text: '保司结案'
      },
      {
        type: 'polyline',
        sourceNodeId: 9,
        targetNodeId: 10
      },
      {
        type: 'polyline',
        sourceNodeId: 20,
        targetNodeId: 2
      },
      {
        type: 'polyline',
        sourceNodeId: 4,
        startPoint: {
          x: 950,
          y: 100
        },
        targetNodeId: 20,
        text: '未通过'
      },
      {
        type: 'polyline',
        sourceNodeId: 4,
        targetNodeId: 60,
        text: '保司拒赔'
      },
      {
        type: 'polyline',
        sourceNodeId: 2,
        targetNodeId: 30,
        text: '未通过'
      },
      {
        type: 'polyline',
        sourceNodeId: 30,
        targetNodeId: 999,
        startPoint: {
          x: 500,
          y: 320
        },
        endPoint: {
          x: 100,
          y: 200
        }
        //   text: '未通过'
      },
      {
        type: 'polyline',
        sourceNodeId: 6,
        targetNodeId: 50,
        text: '不接受'
      },
      {
        type: 'polyline',
        sourceNodeId: 50,
        targetNodeId: 999,
        startPoint: {
          x: 1550,
          y: 320
        },
        endPoint: {
          x: 100,
          y: 200
        }
      },
      {
        type: 'polyline',
        sourceNodeId: 40,
        targetNodeId: 999,
        endPoint: {
          x: 100,
          y: 100
        }
      }
    ]
  }
})

onMounted(() => {
  const lf = new LogicFlow({
    container: document.querySelector('#container'),
    stopScrollGraph: true,
    stopMoveGraph: false,
    stopZoomGraph: false,
    isSilentMode: true, //仅浏览，不编辑
    adjustEdge: false,
    adjustNodePosition: false,
    nodeTextEdit: false,
    edgeTextEdit: false,
    nodeTextDraggable: false,
    edgeTextDraggable: false,
    //   width: 500,
    height: 400,
    style: {
      rect: {
        radius: 6,
        strokeWidth: 1
      },
      diamond: {
        strokeWidth: 1
      },
    }
  })
  lf.zoom(0.6, [0, 150])
  lf.setTheme({
    polyline: {
      // stroke: '#000000',
      strokeWidth: 1
    }
  })

  const getStatus = async () => {
    const res = await queryUserStatusById()
    if (res.code === 200) {
      lipeiJLstatus.value = res.data.lipeiJLstatus
      console.log(lipeiJLstatus.value === 5)
      lipeiJLstatusValue.value = res.data.lipeiJLstatusValue
      lf.render(data.value)
    }
  }
  getStatus()
    console.log(data.value)
  //   lf.render(data)
})
function generateArray(num) {
  let result = [];

  if (num >= 1 && num <= 10) {
    result = Array.from({ length: num }, (_, i) => i + 1);
  } else if (num === 20 || num === 30) {
    result = [1, 2, num];
  } else if (num === 60) {
    result = [1, 2, 3, 4, num];
  } else if (num === 50) {
    result = [1, 2, 3, 4, 5, 6, num];
  } else {
    result = [num]; // 默认只返回参数自身
  }

  // 在最前面加上 999
  return [999, ...result];
}
console.log(generateArray(lipeiJLstatus.value))
</script>
<style lang="scss" scoped></style>
