<template>
  <div class="app-container">
    <div class="section-title">下载文件信息</div>
    <div class="profile-item">
      <label class="item-label">文件上传时间:</label>
      <span class="item-value">{{ fileInfo.uploadTime || "-" }}</span>
    </div>
    <div class="profile-item">
      <label class="item-label">文件标题:</label>
      <span class="item-value">{{ fileInfo.fileName || "-" }}</span>
    </div>
    <div class="profile-item">
      <label class="item-label">文件内容:</label>
      <span class="item-value">
        <el-button type="primary" @click="downloadFile">下载文件</el-button>
      </span>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();

const fileInfo = ref({
  uploadTime: "2025-05-23 10:00:00",
  fileName: "测试文件",
  fileUrl: "https://www.baidu.com",
});

const downloadFile = () => {
/*   const url = fileInfo.value.fileUrl;
  const fileName = fileInfo.value.fileName;
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  a.click(); */
  proxy.$modal.msgSuccess("下载成功");
};
</script>

<style lang="scss" scoped>
.section-title {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  font-size: 17px;
  margin-bottom: 40px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 19px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
}

.profile-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-end;
  margin-bottom: 30px;
  font-size: 14px;
  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    background-color: #ffffff;
  }
  .item-label {
    width: 200px;
    text-align: left;
    margin-right: 10px;
    font-weight: 500;
  }
  .item-value {
    flex: 1;
    color: #767676;
  }
}

.actions {
  margin-top: 32px;
  .el-button {
    padding: 0px 30px;
  }
}
</style>
