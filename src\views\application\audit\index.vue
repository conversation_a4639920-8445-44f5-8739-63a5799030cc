<template>
  <div class="application-query-container">
    <!-- 筛选区域 -->
    <!-- 筛选表单 -->
    <el-form
      :model="queryForm"
      ref="queryRef"
      label-width="140px"
      :inline="false"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="申请人姓名">
            <el-input
              v-model="queryForm.applicantName"
              placeholder="请输入被申请人姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8"
          ><el-form-item label="参保人姓名">
            <el-input
              v-model="queryForm.insuredName"
              placeholder="请输入参保人姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是申请人的什么">
            <el-select
              v-model="queryForm.relation"
              placeholder="请选择关系"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in relationOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="签署时间范围">
            <el-date-picker
              v-model="queryForm.signTimeRange"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请状态">
            <el-select
              v-model="queryForm.status"
              placeholder="请选择申请状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="text-align: right;">
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <!-- 数据表格 -->
    <div>
      <el-table :data="allData" style="width: 100%">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column
          prop="applicantName"
          label="申请人姓名"
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="insuredName"
          label="参保人姓名"
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="relationText"
          label="是申请人的什么"
          min-width="130"
          align="center"
        />
        <el-table-column prop="applyTime" label="申请时间" min-width="180" align="center"/>

        <el-table-column label="申请状态" min-width="140" align="center">
          <template #default="{ row }">
            <el-tag :type="dataStatus(row.status)">{{ row.statusStr }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="checkClaim">{{ row.status === '1' || row.status === '3' ? '审核' : '查看' }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        class="pagination"
      />
    </div>
  </div>
</template>

<script setup>
import { formatDate } from "@/utils/index";
import { queryUserStatusById,updateUserStatusById } from '@/api/testFlow/index'

import { useRouter } from "vue-router";

const router = useRouter();
// 查询表单数据
const queryForm = reactive({
  applicantName: "",
  insuredName: "",
  relation: "",
  status: "",
  signTimeRange: [],
});

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 关系选项
const relationOptions = ref([
  { value: "self", label: "本人" },
  { value: "spouse", label: "配偶" },
  { value: "child", label: "子女" },
  { value: "parent", label: "父母" },
]);

// 状态选项
const statusOptions = ref([
  { value: "pending", label: "待审核" },
  { value: "pre_confirmation", label: "预赔付金额待确认" },
  { value: "approved", label: "审核通过" },
  { value: "rejected", label: "审核不通过" },
]);

// 模拟数据源 - 根据图片内容创建
const allData = ref([]);

// 当前显示表格数据
const tableData = computed(() => {
  return allData
});

const dataStatus = (status) => {
  switch (status) {
    case "1":
    case "3":
      return "info";
    case "5":
    case "7":
      return "warning";
    case "9":
      return "success";
    default:
      return "danger";
  }
}

// 查询处理
const handleQuery = () => {
  // 实际项目中应该是API请求，这里使用前端过滤模拟
  const { applicantName, insuredName, relation, status, signTimeRange } =
    queryForm;

  const filtered = allData.value.filter((item) => {
    // 申请人姓名筛选
    if (applicantName && !item.applicantName.includes(applicantName))
      return false;

    // 参保人姓名筛选
    if (insuredName && !item.insuredName.includes(insuredName)) return false;

    // 关系筛选
    if (relation && item.relation !== relation) return false;

    // 状态筛选
    if (status && item.status !== status) return false;

    // 时间范围筛选
    if (signTimeRange && signTimeRange.length === 2) {
      const [start, end] = signTimeRange;
      if (item.applyTime < start || item.applyTime > end) return false;
    }

    return true;
  });

  allData.value = filtered;
  pagination.total = filtered.length;
  pagination.currentPage = 1;
};

// 重置表单
const handleReset = () => {
  queryForm.applicantName = "";
  queryForm.insuredName = "";
  queryForm.relation = "";
  queryForm.status = "";
  queryForm.signTimeRange = [];
  handleQuery();
};

const checkClaim = () => {
  router.push("/application/claimaudit");
}

// 组件挂载时初始化数据
onMounted(() => {
  const mockData = {
    id: '1213123321323',
    applicantName: '宁涛',
    insuredName: '宁涛',
    relation: '本人',
    relationText: '本人',
    status: 'pre_confirmation',
    statusStr: "预赔付金额待确认",
    applyTime: formatDate(Date())
  }
  queryUserStatusById().then(
    status => {
      const resStatus = status.data
      if (resStatus.lipeiJLstatus == 1 || resStatus.lipeiJLstatus == 3 || resStatus.lipeiJLstatus == 20) {
        mockData.status = '1'
        mockData.statusStr = "待审核"
      } else if (resStatus.lipeiJLstatus == 5) {
        mockData.status = '5'
        mockData.statusStr = "预赔付金额待确认"
      } else if (resStatus.lipeiJLstatus == 7) {
        mockData.status = '7'
        mockData.statusStr = "待结案"
      } else if (resStatus.lipeiJLstatus == 9) {
        mockData.status = '9'
        mockData.statusStr = "已结案"
      } else if (resStatus.lipeiJLstatus == 30) {
        mockData.status = '30'
        mockData.statusStr = "申请已驳回"
      } else if (resStatus.lipeiJLstatus == 40) {
        mockData.status = '40'
        mockData.statusStr = "申请已撤销"
      } else if (resStatus.lipeiJLstatus == 50) {
        mockData.status = '50'
        mockData.statusStr = "申请已终止"
      } else if (resStatus.lipeiJLstatus == 60) {
        mockData.status = '60'
        mockData.statusStr = "已拒赔"
      } else {
      }
      allData.value = [mockData];
      if (!resStatus.lipeiJLstatus || resStatus.lipeiJLstatus == 1 || resStatus.lipeiJLstatus == 20) {
        allData.value = []
      }
    }  
  )
  allData.value = [mockData];
  pagination.total = 1;
});
</script>

<style lang="scss" scoped>
.application-query-container {
  padding: 20px;
  background-color: white;
  min-height: 100vh;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .filter-form {
    padding: 15px;

    :deep(.el-form-item) {
      margin-bottom: 0;
      width: 100%;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
        padding-right: 10px;
      }

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }
}

.button-container {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 5px;

  .el-button {
    width: 100%;
    max-width: 100px;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 10px;
}

@media (max-width: 768px) {
  .filter-card {
    .el-col {
      margin-bottom: 15px;
    }
  }

  .button-container {
    justify-content: center;
    padding-top: 0;

    .el-button {
      max-width: 120px;
    }
  }
}
</style>
