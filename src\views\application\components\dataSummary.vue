<template>
  <div class="medical-records-container">
    <el-table :data="medicalData" style="width: 100%" @selection-change="handleSelectionChange">
      <!-- 序号列（带多选） -->
      <el-table-column fixed type="selection" width="30" align="center"></el-table-column>
      
      <!-- 序号 -->
      <el-table-column label="序号" width="60" type="index"  align="center">
      </el-table-column>
      
      <!-- 医疗机构 -->
      <el-table-column prop="institution" label="医疗机构" min-width="160" align="center">
      </el-table-column>
      
      <!-- 就诊开始时间 -->
      <el-table-column prop="startTime" label="就诊开始时间" min-width="180" align="center">
      </el-table-column>
      
      <!-- 就诊结束时间 -->
      <el-table-column prop="endTime" label="就诊结束时间" min-width="180" align="center">
      </el-table-column>
      
      <!-- 就诊科室 -->
      <el-table-column prop="department" label="就诊科室" min-width="140" align="center">
      </el-table-column>
      
      <!-- 就诊流水号 -->
      <el-table-column prop="serialNo" label="就诊流水号" min-width="180" align="center">
      </el-table-column>
      
      <!-- 操作 -->
      <el-table-column label="操作" width="150" fixed="right" align="center">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleRequest(row)">
            {{ row.hasDetail ? '查看详情' : '请求数据详情' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 批量请求按钮 -->
    <div class="batch-actions">
      <el-button type="primary" link  @click="handleBatchRequest">批量请求</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
const router = useRouter()
// 模拟医疗数据
const medicalData = ref([
  {
    institution: "医科大学总医院",
    startTime: "2025-06-07 09:16",
    endTime: "2025-06-12 12:36",
    department: "骨科",
    serialNo: "SA1856166465F",
    hasDetail: false
  }
]);

const handleSelectionChange = (rows) => {
  console.log(rows[0].institution);
  
}

// 处理请求数据详情
const handleRequest = (row) => {
  console.log('请求数据详情:', row);
  // 实际业务中这里会调用API请求详情数据
  if(!row.hasDetail) {
    row.hasDetail = true
  } else {
    router.push("/medical/dataManagerDetail");
  }
  
};

// 处理批量请求
const handleBatchRequest = () => {
  console.log('执行批量请求操作');
  // 实际业务中这里会处理所有选中的行数据
  medicalData.value.forEach((item) => item.hasDetail = true)
};
</script>

<style lang="scss" scoped>
.medical-records-container {
  background-color: #ffffff;
  overflow-x: hidden;
  .batch-actions {
    margin-top: 20px;
    text-align: left;
    
  }
}
</style>