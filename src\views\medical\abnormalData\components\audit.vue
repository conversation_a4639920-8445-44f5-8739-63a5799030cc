<template>
  <el-dialog
    v-model="dialogVisible"
    title="异常数据审核"
    width="600px"
    :before-close="handleClose"
  >
    <el-form 
      ref="formRef"
      :model="formData" 
      :rules="formRules"
      label-width="120px"
    >
      <!-- 审核项 -->
      <el-form-item label="审核项：" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="normal">此数据正常</el-radio>
          <el-radio label="invalid">作废此数据</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 关联异常编号 -->
      <el-form-item label="关联异常编号：" prop="relatedIds">
        <div>OP1911195727a01
        </div>
      </el-form-item>

      <!-- 审核备注 -->
      <el-form-item label="审核备注：" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入审核备注"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 附件上传 -->
      <el-form-item label="附件图片：">
        <el-upload
          v-model:file-list="formData.files"
          action="#"
          multiple
          :limit="5"
          :before-upload="beforeUpload"
          accept=".jpg,.png"
        >
          <el-button type="primary" link>上传图片</el-button>
          <template #tip>
            <div class="upload-tip">
              上传图片格式：jpg、png，文件大小≤5M
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

const props = defineProps({
  visible: Boolean,
  relatedExceptions: {
    type: Array,
    default: () => ['OP1911195727a01']
  }
})

const emit = defineEmits(['update:visible', 'submit'])

// 表单数据
const formRef = ref()
const formData = reactive({
  status: 'normal',
  remark: '',
  files: []
})

// 验证规则
const formRules = reactive({
  status: [
    { required: true, message: '请选择审核状态', trigger: 'change' }
  ],
  relatedIds: [
    { 
      validator: (_, v, cb) => {
        props.relatedExceptions.length > 0 
          ? cb() 
          : cb(new Error('至少关联一个异常编号'))
      },
      trigger: 'change'
    }
  ],
  remark: [
    { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
  ]
})

// 文件上传校验
const beforeUpload = (file) => {
  const isImage = ['image/jpeg', 'image/png'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 <= 5

  if (!isImage) {
    ElMessage.error('只能上传JPG/PNG格式!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB!')
    return false
  }
  return true
}

// 对话框显示控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (v) => emit('update:visible', v)
})

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    emit('submit', formData)
    handleClose()
  } catch (e) {
    console.error('表单验证失败:', e)
  }
}

// 关闭处理
const handleClose = () => {
  formRef.value.resetFields()
  dialogVisible.value = false
}

// 查看关联项
const handleView = (id) => {
  console.log('查看异常:', id)
}
</script>

<style lang="scss" scoped>
.related-list {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  width: 100%;

  .related-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 0;
    border-bottom: 1px solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    .id {
      color: #409eff;
      font-family: monospace;
    }
  }
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 6px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  
  .el-dialog__header {
    border-bottom: 1px solid #e4e7ed;
    margin-right: 0;
  }
  
  .el-form-item {
    margin-bottom: 18px;
    
    &__label {
      color: #606266;
      font-weight: normal;
    }
  }
}

.dialog-footer {
  .el-button {
    width: 90px;
  }
}
</style>