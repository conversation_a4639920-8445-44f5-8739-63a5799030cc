<template>
  <div ref="chartRef" :style="{ width, height }"></div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

// Props & Emits
const props = defineProps({
  option: { type: Object, required: true },
  theme: { type: String, default: 'default' },
  width: { type: String, default: '100%' },
  height: { type: String, default: '400px' },
  autoResize: { type: Boolean, default: true },
  loading: { type: Boolean, default: false }
})
const emit = defineEmits(['chart-click'])

const chartRef = ref(null)
let chartInstance = null

// 初始化
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value, props.theme)
    chartInstance.setOption(props.option)

    chartInstance.on('click', params => {
      emit('chart-click', params)
    })

    // 初始 loading 状态
    if (props.loading) chartInstance.showLoading()
  }
}

watch(
  () => props.option,
  newOption => {
    if (chartInstance && newOption) {
      chartInstance.setOption(newOption, true)
    }
  },
  { deep: true }
)

watch(
  () => props.loading,
  val => {
    if (chartInstance) {
      val ? chartInstance.showLoading() : chartInstance.hideLoading()
    }
  }
)

const resizeChart = () => {
  chartInstance?.resize()
}

onMounted(() => {
  initChart()
  if (props.autoResize) window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
  if (props.autoResize) window.removeEventListener('resize', resizeChart)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped></style>
