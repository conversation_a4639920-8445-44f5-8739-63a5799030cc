<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" label-width="150px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="用户姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入用户姓名"
              maxlength="50"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="注册手机号" prop="phone">
            <el-input
              v-model="queryParams.phone"
              placeholder="请输入注册手机号"
              clearable
              maxlength="50"
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="注册时间范围" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="实名认证状态" prop="realNameStatus">
            <el-select
              v-model="queryParams.realNameStatus"
              multiple
              placeholder="请选择实名认证状态"
              style="width: 100%"
              clearable
              maxlength="50"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in realNameStatusList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="账号状态" prop="accountStatus">
            <el-select
              v-model="queryParams.accountStatus"
              multiple
              placeholder="请选择账号状态"
              style="width: 100%"
              clearable
              maxlength="50"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in accountStatusList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" align="right">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="userList"
      :row-style="{ height: '45px' }"
    >
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column
        label="用户姓名"
        align="center"
        prop="name"
        width="200"
      />e" />
      <el-table-column label="注册手机号" align="center" prop="phone" />
      <el-table-column
        label="注册时间"
        align="center"
        prop="time"
        width="200"
      />
      <el-table-column label="实名认证状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.isRealName === 1 "  type="success"
            >已实名</el-tag
          >
          <el-tag v-else type="danger"
            >未实名</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" key="accountStatus">
        <template #default="scope">
          <el-switch
            v-model="scope.row.accountStatus"
            active-value="1"
            inactive-value="2"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="100"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:post:remove']"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Users">
import { listUsers } from "@/api/insured/user";
import { queryUserStatusById } from "@/api/testFlow/index";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const userList = ref([]);
const loading = ref(true);
const total = ref(1);

// 实名认证状态下拉选项
const realNameStatusList = ref([
  {
    label: "已实名",
    value: "1",
  },
  {
    label: "未实名",
    value: "2",
  },
]);

// 账号状态   开启 关闭 已注销
const accountStatusList = ref([
  {
    label: "开启",
    value: "1",
  },
  {
    label: "关闭",
    value: "2",
  },
  {
    label: "已注销",
    value: "3",
  },
]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    phone: undefined,
    realNameStatus: undefined,
    accountStatus: undefined,
  },
  dateRange: [],
});

const { queryParams, dateRange } = toRefs(data);

/** 查询参保用户列表 */
async function  getList() {
  loading.value = true;
  let listResponse = await listUsers(proxy.addDateRange(queryParams.value, dateRange.value))
  let statusResponse = await queryUserStatusById();
  let listData = listResponse.rows;
  listData[0].isRealName = statusResponse.data.isRealName || 0;
  userList.value = listData;
  total.value = listResponse.total;
  loading.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  dateRange.value = [];
  handleQuery();
}

/** 用户状态修改  */
function handleStatusChange(row) {
  // let text = row.status === "0" ? "启用" : "停用";
  // proxy.$modal
  //   .confirm('确认要"' + text + '""' + row.name + '"用户吗?')
  //   .then(function () {
  //     return ""; // changeUserStatus(row.userId, row.status);
  //   })
  //   .then(() => {
  //     proxy.$modal.msgSuccess(text + "成功");
  //   })
  //   .catch(function () {
  //     row.status = row.status === "0" ? "1" : "0";
  //   });
}
function handleDetail(row) {
  proxy.$router.push("/insured/userdetail?userId=" + 1);
}

getList();
</script>
