<template>
  <div class="abnormal-data-page">
    <div class="abnormal-header">
      <span>异常编号：OP1911195727250408a01</span>
      <el-icon style="font-weight: 100" color="f9f9f9"><ArrowRight /></el-icon>
    </div>
    <!-- 基础信息 -->
    <div class="abnormal-content">
      <div class="title-info">
        <div class="title">基础信息</div>
        <div class="line"></div>
      </div>
      <div class="content-cell">
        <div>数据接入时间：</div>
        <div>2024-09-01 16:53:19</div>
      </div>
      <div class="content-cell">
        <div>数据异常时间：</div>
        <div>2024-09-01 16:53:19</div>
      </div>
      <div class="content-cell">
        <div>医疗机构名称：</div>
        <div>我是医疗机构名称</div>
      </div>
      <div class="content-cell">
        <div>关联异常编号：</div>
        <div>
          <div>
            <el-button type="primary" link>OP1911195727250408a01</el-button>
          </div>
          <div>
            <el-button type="primary" link>OP1911195727250408a02</el-button>
          </div>
          <div>
            <el-button type="primary" link>OP1911195727250408a03</el-button>
          </div>
        </div>
      </div>
      <div class="content-cell">
        <div>异常数据状态：</div>
        <div>已作废</div>
      </div>
      <div class="content-cell">
        <div>异常数据所属人：</div>
        <div>张三</div>
      </div>
    </div>
    <!-- 异常信息1 -->
    <div class="abnormal-content" style="margin-top: 30px">
      <div class="title-info">
        <div class="title">异常信息（一）</div>
        <div class="line"></div>
      </div>
      <div class="content-cell">
        <div>异常数据类型：</div>
        <div>费用数据异常</div>
      </div>
      <div class="content-cell">
        <div>数据异常信息：</div>
        <div>结算信息中，总费用为负数</div>
      </div>
    </div>
    <!-- 异常信息2 -->
    <div class="abnormal-content" style="margin-top: 30px">
      <div class="title-info">
        <div class="title">异常信息（二）</div>
        <div class="line"></div>
      </div>
      <div class="content-cell">
        <div>异常数据类型：</div>
        <div>费用数据异常</div>
      </div>
      <div class="content-cell">
        <div>数据异常信息：</div>
        <div>结算信息中，总费用为负数</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 异常编号
const abnormalNumber = ref("OP1911195727250408a01");
// 是否显示详细信息
const isDetailsVisible = ref(false);
// 数据结构
const sections = ref([
  {
    title: "基础信息",
    items: [
      { label: "数据接入时间", value: "2024-09-01 16:53:19", isLink: false },
      { label: "数据异常时间", value: "2024-09-01 16:53:19", isLink: false },
      { label: "医疗机构名称", value: "我是医疗机构名称", isLink: false },
      {
        label: "关联异常编号",
        value: [
          { id: "OP1911195727250408a01", canView: true },
          { id: "OP1911195727250408a02", canView: true },
          { id: "OP1911195727250408a03", canView: true },
        ],
        isLink: true,
      },
      { label: "异常数据状态", value: "已作废", isLink: false },
      { label: "异常数据所属人", value: "张三", isLink: false },
    ],
  },
  {
    title: "异常信息（一）",
    items: [
      { label: "数据异常类型", value: "费用数据异常", isLink: false },
      {
        label: "数据异常信息",
        value: "结算信息中，总费用为负数",
        isLink: false,
      },
    ],
  },
  {
    title: "异常信息（二）",
    items: [
      { label: "数据异常类型", value: "费用数据异常", isLink: false },
      {
        label: "数据异常信息",
        value: "结算信息中，总费用为负数",
        isLink: false,
      },
    ],
  },
]);

// 切换详细信息显示隐藏
const toggleDetails = () => {
  isDetailsVisible.value = !isDetailsVisible.value;
};

// 查看关联异常（可根据实际需求完善逻辑，如跳转路由等）
const viewRelatedAbnormal = (id) => {
  console.log("查看关联异常编号:", id);
};
</script>

<style lang="scss" scoped>
.abnormal-header {
  margin: 10px 20px 30px;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #f6f6f6;
  padding-bottom: 20px;

  display: flex;
  align-items: center;
  justify-content: space-between;
}
.abnormal-content {
  margin: 10px;
  font-size: 14px;
}
.title-info {
  display: flex;
  align-items: center;
  .line {
    height: 1px;
    background-color: rgba(238, 238, 238);
    flex: 1;
  }

  .title {
    display: flex;
    font-size: 15px;
    font-weight: 500;
    align-items: center;
    margin-left: -10px;
    margin-right: 15px;
  }

  .title::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 15px;
    background: #2d8cf0;
    border-radius: 3px;
    margin-right: 5px;
  }
}
.content-cell {
  padding-top: 20px;
  display: flex;
  margin: 10px 0;
  gap: 50px;
}
</style>
