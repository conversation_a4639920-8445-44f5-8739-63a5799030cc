<template>
  <el-upload
    class="custom-upload"
    :drag="true"
    :multiple="false"
    :show-file-list="false"
    :http-request="handleUpload"
    :on-progress="handleProgress"
    :on-success="handleSuccess"
    :on-error="handleError"
  >
    <div class="upload-content">
      <!-- 云朵图标 + 箭头 -->
      <div class="upload-icon-wrapper">
        <el-icon class="cloud-icon">
          <Cloud />
        </el-icon>
        <el-icon class="arrow-icon">
          <Top />
        </el-icon>
      </div>
      
      <!-- 文字提示 -->
      <div class="upload-text">
        <p>将文件拖到此处</p>
        <p>或<span class="click-link">点击上传</span></p>
      </div>
    </div>
    
    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress 
        :percentage="uploadProgress" 
        status="success" 
        :show-text="false" 
        :stroke-width="6" 
      />
      <span class="upload-message">上传中 {{ uploadProgress }}%</span>
    </div>
    
    <!-- 已上传文件 -->
    <div v-if="uploadedFile" class="uploaded-info">
      <el-icon class="file-icon"><Document /></el-icon>
      <span class="file-name">{{ uploadedFile.name }}</span>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <el-icon class="error-icon"><Warning /></el-icon>
      <span>{{ errorMessage }}</span>
    </div>
  </el-upload>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  // 上传URL
  action: {
    type: String,
    required: true
  },
  // 请求头
  headers: {
    type: Object,
    default: () => ({})
  },
  // 文件类型限制
  fileTypes: {
    type: Array,
    default: () => [
      'image/jpeg', 
      'image/png', 
      'application/pdf', 
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['success', 'error', 'update:modelValue']);

const uploading = ref(false);
const uploadProgress = ref(0);
const uploadedFile = ref(null);
const errorMessage = ref(null);

// 处理自定义上传
const handleUpload = (options) => {
  const { file } = options;
  
  // 验证文件类型
  if (props.fileTypes.length > 0 && !props.fileTypes.includes(file.type)) {
    errorMessage.value = `不支持${getFileType(file.type)}格式`;
    return Promise.reject(`不支持的文件类型: ${file.type}`);
  }
  
  // 验证文件大小
  const maxSizeBytes = props.maxSize * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    errorMessage.value = `文件大小不能超过${props.maxSize}MB`;
    return Promise.reject(`文件大小超过限制: ${file.size}`);
  }
  
  // 重置状态
  uploading.value = true;
  uploadProgress.value = 0;
  errorMessage.value = null;
  uploadedFile.value = null;
  
  // 创建FormData
  const formData = new FormData();
  formData.append('file', file);
  
  // 构建请求
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    // 进度监听
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        uploadProgress.value = progress;
      }
    });
    
    // 完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        const response = JSON.parse(xhr.responseText);
        uploading.value = false;
        uploadedFile.value = file;
        emit('success', response);
        emit('update:modelValue', response.url);
        resolve(response);
      } else {
        handleError(new Error(`上传失败, 状态码: ${xhr.status}`));
        reject(xhr);
      }
    });
    
    // 错误处理
    xhr.addEventListener('error', () => {
      handleError(new Error('网络错误，上传失败'));
      reject(new Error('网络错误'));
    });
    
    // 发送请求
    xhr.open('POST', props.action, true);
    
    // 设置请求头
    Object.entries(props.headers).forEach(([key, value]) => {
      xhr.setRequestHeader(key, value);
    });
    
    xhr.send(formData);
  });
};

// 处理上传进度
const handleProgress = (event, file) => {
  console.log('上传进度:', event.percent);
};

// 处理上传成功
const handleSuccess = (response, file) => {
  console.log('上传成功:', response);
};

// 处理上传错误
const handleError = (error, file) => {
  console.error('上传失败:', error);
  uploading.value = false;
  
  if (error.message.includes('exceeded the size limit')) {
    errorMessage.value = `文件大小不能超过${props.maxSize}MB`;
  } else if (error.message.includes('file type not supported')) {
    errorMessage.value = '不支持的文件类型';
  } else {
    errorMessage.value = '上传失败，请重试';
  }
  
  emit('error', error);
};

// 获取文件类型名称
const getFileType = (mimeType) => {
  const types = {
    'image/jpeg': 'JPG',
    'image/png': 'PNG',
    'application/pdf': 'PDF',
    'application/msword': 'DOC',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX'
  };
  
  return types[mimeType] || mimeType;
};

// 重置上传状态
const reset = () => {
  uploadedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
  errorMessage.value = null;
};

defineExpose({ reset });
</script>

<style lang="scss" scoped>
.custom-upload {
  :deep(.el-upload) {
    width: 100%;
  }
  
  :deep(.el-upload-dragger) {
    width: 100%;
    min-height: 200px;
    padding: 40px 20px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    background-color: #fafafa;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #409eff;
      background-color: #f0f9ff;
    }
  }
  
  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    .upload-icon-wrapper {
      position: relative;
      margin-bottom: 20px;
      
      .cloud-icon {
        font-size: 60px;
        color: #b9c0cc;
      }
      
      .arrow-icon {
        position: absolute;
        top: -5px;
        right: -8px;
        font-size: 20px;
        color: #409eff;
        background: white;
        border-radius: 50%;
        padding: 2px;
        transform: translateY(-10px);
        animation: bounce 1.5s infinite;
      }
    }
    
    .upload-text {
      font-size: 14px;
      color: #606266;
      
      p {
        margin: 2px 0;
      }
      
      .click-link {
        color: #409eff;
        font-weight: 500;
        margin-left: 4px;
        text-decoration: underline;
        cursor: pointer;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
  
  .upload-progress {
    margin-top: 25px;
    width: 80%;
    max-width: 300px;
    
    .upload-message {
      display: block;
      margin-top: 8px;
      font-size: 13px;
      color: #606266;
    }
  }
  
  .uploaded-info {
    margin-top: 25px;
    display: flex;
    align-items: center;
    background: #f0f9eb;
    border: 1px solid #e1f3d8;
    border-radius: 6px;
    padding: 8px 15px;
    
    .file-icon {
      font-size: 20px;
      color: #67c23a;
      margin-right: 10px;
    }
    
    .file-name {
      font-size: 14px;
      color: #303133;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .error-message {
    margin-top: 20px;
    color: #f56c6c;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .error-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) translateX(0);
  }
  40% {
    transform: translateY(-15px) translateX(-2px);
  }
  60% {
    transform: translateY(-7px) translateX(1px);
  }
}
</style>