<template>
  <el-row class="app-container">
    <el-col :span="13">
      <div class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          label-width="120px"
          label-position="left"
          :rules="formRules"
        >
          <h3 class="section-title">录入申请信息</h3>
          <!-- 医院名称 -->
          <el-form-item label="医院机构名称" prop="hospitalName">
            <el-select
              v-model="formData.hospitalName"
              filterable
              placeholder="请选择医疗机构名称"
              style="width: 100%"
              clearable
              maxlength="50"
            >
              <el-option
                v-for="dict in hospitalNameList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              v-model="formData.contactName"
              placeholder="请输入联系人姓名"
            />
          </el-form-item>
          <el-form-item label="联系人手机号码" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入联系人手机号码"
            />
          </el-form-item>
          <el-form-item label="联系人邮箱" prop="contactEmail">
            <el-input
              v-model="formData.contactEmail"
              placeholder="请输入联系人邮箱"
            />
          </el-form-item>
          <h3 class="section-title second">录入申请文件</h3>
          <!-- 文件上传区域 -->
          <div class="file-uploads"></div>
          <el-row>
            <el-col :span="11">
              <el-form-item
                label="企业营业执照"
                label-position="top"
                class="label-top"
              >
                <el-upload
                  class="upload-demo"
                  drag
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                  :on-change="handleLicenseChange"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="upload-tips">
                      <div>推荐上传"扫描件"</div>
                      <div>文件格式：pdf/jpg/png</div>
                      <div>文件大小：≤5M</div>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col class="text-center" :span="2">
              <span class="text-gray-500"></span>
            </el-col>
            <el-col :span="11">
              <el-form-item
                label="数据传输合规承诺书"
                label-position="top"
                class="label-top"
              >
                <el-upload
                  class="upload-demo"
                  drag
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                  :on-change="handleCommitmentChange"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="upload-tips">
                      <div>推荐上传"扫描件"</div>
                      <div>文件格式：pdf/jpg/png</div>
                      <div>文件大小：≤5M</div>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-actions">
        <div class="right">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";

const router = useRouter();
const formRef = ref(null);
const formData = ref({
  hospitalName: "",
  contactName: "",
  contactPhone: "",
  contactEmail: "",
  licenseFile: null,
  commitmentFile: null,
});

const formRules = {
  hospitalName: [
    { required: true, message: "请选择医院机构名称", trigger: "blur" },
  ],
  contactName: [
    { required: true, message: "请输入联系人姓名", trigger: "blur" },
  ],
  contactPhone: [
    { required: true, message: "请输入联系人手机号码", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  contactEmail: [
    { required: true, message: "请输入联系人邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
  ],
};

// 医疗结构名称
const hospitalNameList = ref([
  {
    label: "天津中医院",
    value: "1",
  },
  {
    label: "天津一中心医院",
    value: "2",
  },
]);

const beforeUpload = (file) => {
  const isAcceptFormat = [
    "image/jpeg",
    "image/png",
    "application/pdf",
  ].includes(file.type);
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isAcceptFormat) {
    ElMessage.error("只能上传jpg/png/pdf格式的文件!");
    return false;
  }
  if (!isLt5M) {
    ElMessage.error("文件大小不能超过5MB!");
    return false;
  }
  return true;
};

const handleLicenseChange = (file) => {
  formData.value.licenseFile = file.raw;
};

const handleCommitmentChange = (file) => {
  formData.value.commitmentFile = file.raw;
};

const handleCancel = () => {
  ElMessage.info("已取消申请");
  router.push("/medical/verify");
};

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success("申请提交成功");
      router.go(-1);
      // 这里可以添加提交表单的逻辑
    } else {
      ElMessage.error("请填写完整表单");
      return false;
    }
  });
};
</script>

<style scoped lang="scss">
.app-container {
  background-color: #fff;
}
.section-title {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  font-size: 16px;
  margin: 10px 0px 20px 0px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 18px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
  &.second {
    margin-top: 50px;
  }
}

:deep(.label-top) {
  display: flex;
  flex-direction: column;
}

.upload-demo {
  width: 100%;
}

.upload-tips {
  margin-top: 5px;
  font-size: 12px;
  color: #999;
  div {
    height: 25px;
  }
}

.footer-actions {
  width: 100%;
  margin-top: 30px;
  padding-top: 20px;
  display: flex;
  justify-content: center;
  .el-button {
    margin: 0 10px;
    width: 100px;
  }
}
</style>
