<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" label-width="150px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="申请人姓名：" prop="applyName">
            <el-input
              v-model="queryParams.applyName"
              placeholder="请输入申请人姓名"
              maxlength="50"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="参保人姓名：" prop="insuredName">
            <el-input
              v-model="queryParams.insuredName"
              placeholder="请输入申请人姓名"
              maxlength="50"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是申请人的什么：" prop="relationship">
            <el-select
              v-model="queryParams.relationship"
              multiple
              filterable
              placeholder="请选择是申请人的什么"
              style="width: 100%"
              clearable
              maxlength="50"
              @keyup.enter="handleQuery"
            >
              <el-option v-for="dict in relationshipList" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="申请时间范围：" prop="dateRange">
            <el-date-picker
              v-model="queryParams.dateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保险公司：" prop="company">
            <el-select v-model="queryParams.company" placeholder="请选择保险公司" clearable style="width: 100%" @keyup.enter="handleQuery">
              <el-option v-for="dict in companyList" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请状态：" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择申请状态"
              filterable
              clearable
              maxlength="50"
              style="width: 100%"
              @keyup.enter="handleQuery"
            >
              <el-option v-for="dict in statusList" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 20px">
        <el-col :span="24" style="text-align: right">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="tableData" :row-style="{ height: '45px' }">
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="申请人姓名" align="center" prop="applyName" width="200" />
      <el-table-column label="参保人姓名" align="center" prop="insuredName" width="200" />
      <el-table-column label="是申请人的什么" align="center" prop="relationship" />
      <el-table-column label="申请时间" align="center" prop="dateRange"> </el-table-column>
      <el-table-column label="保险公司" align="center" prop="company" width="150" />
      <el-table-column label="申请状态" align="center" prop="status" width="150" >
        <template  #default>
          <el-tag>{{ formatStatusText(lipeiJLstatus) }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="100" align="center" fixed="right" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)" >查看</el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)" >审核</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Verify">
import { getInsuredList } from '@/api/insured/verify'
import { queryUserStatusById,updateUserStatusById } from '@/api/testFlow/index'
const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(true)
const total = ref(0)

// // 医疗结构名称
// const medicalNameList = ref([
//   {
//     label: "天津中医院",
//     value: "1",
//   },
//   {
//     label: "天津一中心医院",
//     value: "2",
//   },
// ]);

// // 审核处理所属人
// const assessorList = ref([
//   {
//     label: "张三",
//     value: "1",
//   },
//   {
//     label: "李四",
//     value: "2",
//   },
// ]);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  applyName: '',
  insuredName: '',
  relationship: '',
  dateRange: [],
  company: '',
  status: ''
})
const relationshipList = ref([
  { label: '本人', value: '1' },
  { label: '配偶', value: '2' },
  { label: '父亲', value: '3' },
  { label: '母亲', value: '4' },
  { label: '儿子', value: '5' },
  { label: '女儿', value: '6' }
])
const statusList = ref([
  { label: '待审核', value: '0' },
  { label: '待保司审核', value: '1' },
  { label: '预赔付金额待确认', value: '2' },
  { label: '待结案', value: '3' },
  { label: '保司驳回申请，待重新审核', value: '4' },
  { label: '申请已驳回', value: '5' },
  { label: '申请已撤销', value: '6' },
  { label: '申请已终止', value: '7' },
  { label: '已拒赔', value: '8' },
  { label: '账号已冻结', value: '9' }
])
const companyList = ref([
  { label: '平安保险', value: '1' },
  { label: '太平洋保险', value: '2' },
  { label: '中国人寿', value: '3' },
])
function getList() {
  loading.value = true
  getInsuredList(queryParams.value).then(response => {

      tableData.value = response.data
      total.value = response.total
    loading.value = false
    getStatus()
  })
}

const lipeiJLstatus = ref(0)
const lipeiJLstatusValue = ref('')
const getStatus = async () => {
  const res = await queryUserStatusById( )
  if (res.code === 200) {
    lipeiJLstatus.value = res.data.lipeiJLstatus
    lipeiJLstatusValue.value = res.data.lipeiJLstatusValue
    if(lipeiJLstatus.value === 0){
      tableData.value = []
    }
  }

}
getList()

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  dateRange.value = []
  handleQuery()
}

function handleDetail(row) {
  proxy.$router.push('/insured/verify/applyDetails')
}

function formatStatusText(value) {
  const statusMap = {
    1: '待审核',
    3: '待保险公司审核',
    5: '预赔付金额待确认',
    7: '参保人已接收预赔付金额',
    9: '已结案',
    20: '保司驳回申请，待重新审核',
    30: '申请已驳回',
    40: '申请已撤销',
    50: '申请已终止',
    60: '已拒赔',
  }
  return statusMap[value] || '未知状态'
}
</script>
