<template>
  <div class="app-container">
    <!-- 筛选表单 -->
    <el-form :model="queryParams" ref="queryRef" label-width="140px" :inline="false" >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="数据接入时间范围：" prop="entryTimeRange">
            <el-date-picker v-model="dateRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被查询人姓名：" prop="insuredName">
            <el-input v-model="queryParams.insuredName" placeholder="请输入被查询人姓名" clearable />
          </el-form-item>
          
        </el-col>
        <el-col :span="8">
          <el-form-item label="被查询人证件类型：" prop="insuredIdType">
            <el-select v-model="queryParams.insuredIdType" placeholder="请选择被查询人证件类型" clearable style="width: 100%">
              <el-option label="居民身份证" value="居民身份证" />
              <el-option label="护照" value="护照" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="被查询人证件号码：" prop="insuredIdNumber">
            <el-input v-model="queryParams.insuredIdNumber" placeholder="请输入被查询人证件号码" clearable />
          </el-form-item>
        </el-col>
       
        <el-col :span="8">
          <el-form-item label="医疗机构名称：" prop="hospitalName">
            <el-select v-model="queryParams.hospitalName" placeholder="请选择医疗机构名称" clearable style="width: 100%">
              <el-option label="我是医疗机构名称" value="我是医疗机构名称" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="就诊入院时间范围：" prop="appointmentTime">
            <el-date-picker v-model="appointmentRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        
        <el-col :span="8">
          <el-form-item label="就诊流水号：" prop="visitNumber">
            <el-input v-model="queryParams.visitNumber" placeholder="请输入就诊流水号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据状态：" prop="dataStatus">
            <el-select v-model="queryParams.dataStatus" placeholder="请选择数据状态" clearable style="width: 100%">
              <el-option label="正常" value="0" />
              <el-option label="异常" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 操作按钮单独一行，右对齐 -->
      <el-row>
        <el-col :span="24">
          <div class="form-actions">
            <el-button type="primary" @click="handleQuery" :icon="Search">查询</el-button>
            <el-button type="primary" plain @click="resetQuery" :icon="RefreshRight">重置</el-button>
            <el-button type="primary" plain @click="handleExport" :icon="Download">导出</el-button>
            <el-button type="warning" plain @click="handleImport" :icon="Upload">
              导入
            </el-button>
            <el-button @click="downloadTemplate">导入模板下载</el-button>


          </div>
        </el-col>
      </el-row>
    </el-form>

    <!-- 数据表格 -->
    <el-table class="table-data" v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" style="margin-top: 20px">
      <el-table-column type="selection" width="50" align="center" fixed="left"/>
      <el-table-column label="序号" align="center" prop="index" width="60" fixed="left"/>
      <el-table-column label="数据接入时间" align="center" prop="registerTime"/>
      <el-table-column label="被查询人姓名" align="center" prop="insuredName" width="120"/>
      <el-table-column label="被查询人证件类型" align="center" prop="insuredIdType" width="130"/>
      <el-table-column label="被查询人证件号码" align="center" prop="insuredIdNumber" width="170"/>
      <el-table-column label="医疗机构名称" align="center" prop="hospitalName" width="170"/>
      <el-table-column label="就诊入院时间" align="center" prop="visitTime"/>
      <el-table-column label="就诊出院时间" align="center" prop="visitEndTime"/>
      <el-table-column label="就诊流水号" align="center" prop="visitNumber" width="170"/>
      <el-table-column label="数据状态" align="center" prop="dataStatus" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.dataStatus == 0 ? 'success' : 'danger'">{{ scope.row.dataStatus == 0 ? '正常' : '异常' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 导入弹窗 -->
    <el-dialog :title="importTitle" v-model="importOpen" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="downloadTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="importOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'
import { Download, Search, Upload, RefreshRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const { proxy } = getCurrentInstance()
const { dataStatusOptions } = proxy.useDict('data_status')

const loading = ref(false)
const importOpen = ref(false)
const importTitle = ref('数据导入')
const total = ref(10)
const dateRange = ref([])
const appointmentRange = ref([])
const dataList = ref([
  { index: 1, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '0' },
  { index: 2, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '0' },
  { index: 3, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '0' },
  { index: 4, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 5, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 6, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 7, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 4, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 4, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 4, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 4, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
  { index: 4, registerTime: '2024-09-01 16:53:19', insuredName: '王磊', insuredIdType: '居民身份证', insuredIdNumber: '230406481548484144', hospitalName: '我是医疗机构名称', visitType: '门诊', visitTime: '2024-09-01', visitEndTime: '2024-09-01', visitNumber: 'OP1911195727', dataStatus: '1' },
])
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    insuredName: '',
    insuredIdType: '',
    insuredIdNumber: '',
    hospitalName: '',
    visitNumber: '',
    dataStatus: ''
  }
})
const { queryParams } = toRefs(data)

const upload = reactive({
  isUploading: false,
  headers: {},
  url: '/api/data/import',
})

function handleQuery() {
  // 查询逻辑
}
function resetQuery() {
  // 重置逻辑
}
function handleExport() {
  // 导出逻辑
}
function handleImport() {
  importOpen.value = true
}
function downloadTemplate() {
  // 下载模板逻辑
}
function handleSelectionChange() {}
function handleView(row) {
  router.push("/medical/dataManagerDetail");
}
function getList() {}
function handleFileUploadProgress() {}
function handleFileSuccess() {}
function submitFileForm() {}
</script>

<style lang="scss" scoped>

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
  margin-top: 10px;
}

.table-data {
  height: calc(100vh - 424px);
}
</style>
