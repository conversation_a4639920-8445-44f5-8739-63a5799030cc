<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" label-width="150px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="被查询人姓名" prop="peopleName">
            <el-select
              v-model="queryParams.peopleName"
              multiple
              filterable
              placeholder="请选择被查询人姓名"
              style="width: 100%"
              clearable
              maxlength="50"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in peopleNameList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被查询人证件类型" prop="type">
            <el-select
              v-model="queryParams.type"
              placeholder="请选择被查询人证件类型"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in typeList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="被查询人证件号码" prop="cardNumber">
            <el-input
              v-model="queryParams.cardNumber"
              placeholder="请输入被查询人证件号码"
              maxlength="50"
              clearable
              style="width: 100%"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 20px">
        <el-col :span="24" style="text-align: right">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button icon="Upload" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="verifyList"
      :row-style="{ height: '45px' }"
    >
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="被查询人姓名" align="center" prop="peopleName" />
      <el-table-column label="被查询人证件类型" align="center" prop="type" />
      <el-table-column
        label="被查询人证件号码"
        align="center"
        prop="cardNumber"
      />
      <el-table-column
        label="操作"
        width="100"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >查看足迹</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Verify">
import { listPatient } from "@/api/footprint/query";

const { proxy } = getCurrentInstance();

const verifyList = ref([]);
const loading = ref(true);
const total = ref(0);

// 医疗结构名称
const peopleNameList = ref([
  {
    label: "张三",
    value: "1",
  },
  {
    label: "李四",
    value: "2",
  },
]);

// 接入阶段
const typeList = ref([
  {
    label: "居名身份证",
    value: "1",
  },
  {
    label: "护照",
    value: "2",
  },
]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    peopleName: undefined,
    type: undefined,
    cardNumber: undefined,
  },
  dateRange: [],
});

const { queryParams } = toRefs(data);

/** 查询岗位列表 */
function getList() {
  loading.value = true;
  listPatient(queryParams.value).then((response) => {
    verifyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.$modal.msgSuccess("导出成功");
  // proxy.download(
  //   "/footprint/patient/export",
  //   queryParams.value,
  //   "患者足迹信息列表.xls"
  // );
}

function handleDetail(row) {
  proxy.$router.push("/footprint/footlist");
}

getList();
</script>
