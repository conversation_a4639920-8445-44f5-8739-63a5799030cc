<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="发布时间范围" style="width: 308px">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="16" style="text-align: right">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button icon="Upload" @click="handleUpload">上传文件</el-button>
        </el-col>
      </el-row>

      <el-form-item> </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="fileList">
      <el-table-column label="序号" type="index" align="center" width="50" />
      <el-table-column
        label="文件名称"
        align="center"
        prop="fileName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="发布人"
        align="center"
        prop="publisher"
        width="200"
      />
      <el-table-column
        label="文件发布时间"
        align="center"
        prop="publicDate"
        width="200"
      >
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)"
            >查看</el-button
          >
          <el-button link type="primary" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
      <!-- 表格为空时自定义内容 -->
      <template #empty>
        <el-empty style="height: 60vh" description="未查询到相关文件" />
      </template>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Message">
import { listFiles } from "@/api/messages/download";
const { proxy } = getCurrentInstance();
const router = useRouter();

const fileList = ref([]);
const loading = ref(true);
const total = ref(0);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileType: undefined,
});
const dateRange = ref([]);

/** 查询列表 */
function getList() {
  loading.value = true;
  listFiles(proxy.addDateRange(queryParams.value, dateRange.value)).then(
    (response) => {
      // 将response.rows中的publicDate倒序排列
      response.rows.sort((a, b) => {
        return new Date(b.publicDate) - new Date(a.publicDate);
      });
      fileList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 上传文件 */
function handleUpload() {
  router.push("/messages/upload");
}

/** 查看文件 */
function handleDetail(row) {
  router.push("/messages/detail");
}

const handleDelete = (row) => {
  proxy.$modal.confirm('此操作将永久删除当前消息，是否继续？').then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}
getList();
</script>

<style lang="scss" scoped></style>
