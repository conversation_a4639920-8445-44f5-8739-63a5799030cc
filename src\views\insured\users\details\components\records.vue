<template>
  <div class="recordDetail">
    <h3 class="section-title">理赔记录</h3>
    <el-table class="recordTable" :data="recordData" style="width: 100%">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column
        prop="applyTime"
        label="申请时间"
        width="180"
        align="center"
      />
      <el-table-column
        prop="applyName"
        label="申请人姓名"
        width="100"
        align="center"
      />
      <el-table-column
        prop="insuredName"
        label="参保人姓名"
        width="100"
        align="center"
      />
      <el-table-column
        prop="relationship"
        label="是申请人的什么"
        width="140"
        align="center"
      />
      <el-table-column prop="medicalNumber" label="就诊流水号" align="center" />
      <el-table-column prop="insuredAgent" label="保险公司" align="center" />
      <el-table-column
        prop="applyStatus"
        label="申请状态"
        width="140"
        align="center"
      />
    </el-table>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import { getInsuredRecord } from "@/api/insured/user";
import { queryUserStatusById } from "@/api/testFlow/index";
const props = defineProps({
  userId: {
    type: String,
    default: () => {
      return "";
    },
  },
});

const recordData = ref([]);
onMounted(async () => {
  let { rows: recordRows } = await getInsuredRecord(props.userId);
  let statusResponse = await queryUserStatusById();
  switch (statusResponse.data.lipeiJLstatus) {
    case 1:
      recordRows[0].applyStatus = "待审核";
      break;
    case 3:
      recordRows[0].applyStatus = "待保司审核";
      break;
    case 5:
      recordRows[0].applyStatus = "预赔付金额待确认";
      break;
    case 7:
      recordRows[0].applyStatus = "待结案";
      break;
    case 9:
      recordRows[0].applyStatus = "已结案";
      break;
    case 20:
      recordRows[0].applyStatus = "保司驳回申请，待重新审核";
      break;
    case 30:
      recordRows[0].applyStatus = "申请已驳回";
      break;
    case 40:
      recordRows[0].applyStatus = "申请已撤销";
      break;
    case 50:
      recordRows[0].applyStatus = "申请已终止";
      break;
    case 60:
      recordRows[0].applyStatus = "已拒赔";
      break;
    case 70:
      recordRows[0].applyStatus = "账号已冻结";
      break;
    default:
      recordRows[0].applyStatus = "-";
      break;
  }  
  recordData.value = recordRows;
});
</script>
<style lang="scss" scoped>
.recordDetail {
  padding: 20px;
  background-color: #fff;
}
.el-table {
  height: 73vh;
}

.section-title {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  font-size: 16px;
  margin: 0px 0px 20px 0px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    display: inline-block;
    width: 4px;
    height: 18px;
    left: 0px;
    background-color: #007bff;
    border-radius: 4px;
    vertical-align: middle;
  }
  &.second {
    margin-top: 50px;
  }
}
</style>
